package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.AutoGenerateMonthReportDTO;
import com.qm.ep.quartz.domain.dto.AutoGenerateWeekReportDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import org.springframework.stereotype.Component;

@Component
public class GeneratePartFinanceReportRemoteHystrix extends QmRemoteHystrix<GeneratePartFinanceReportRemote> implements GeneratePartFinanceReportRemote {


    @Override
    public JsonResultVo generatPartMonthReport(String tenantId, AutoGenerateMonthReportDTO paramsDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo generatPartWeekReport(String tenantId, AutoGenerateWeekReportDTO paramsDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo autoGenerateBeginEndDate(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo generateSpaPenetrationRateInfo(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo autoCal517(String tenantId) {
        return getResult();
    }
}
