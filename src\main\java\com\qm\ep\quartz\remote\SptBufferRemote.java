package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - Spt
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = SptBufferFallbackFactory.class)
public interface SptBufferRemote {

    /**
     * 定时任务 - 缓存直发车辆自动锁车功能
     */
    @GetMapping("/bufferVin/autoLock")
    JsonResultVo<Boolean> autoLock(@RequestHeader("companyId") String companyId,
                                               @RequestHeader("custGroupId") String custGroupId,
                                               @RequestHeader("tenantId") String tenantId,
                                               @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 下线车辆分配APP订单
     */
    @PostMapping("/appOrderMatchVin/autoMatch")
    JsonResultVo<Boolean> otdOrderMatchVin(@RequestHeader("companyId") String companyId,
                                   @RequestHeader("custGroupId") String custGroupId,
                                   @RequestHeader("tenantId") String tenantId,
                                   @RequestHeader("userId") String userId);


}
