package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class UcmsSelFeignFallbackFactory implements FallbackFactory<UcmsSelFeignRemote> {

    @Override
    public UcmsSelFeignRemote create(Throwable throwable) {
        UcmsSelFeignRemoteHystrix feignRemoteHystrix = new UcmsSelFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
