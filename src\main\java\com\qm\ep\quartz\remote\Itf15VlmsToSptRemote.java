package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15VlmsToSptFallbackFactory.class)
public interface Itf15VlmsToSptRemote {

    /**
     * 定时任务 - VlmsToSpt：司机信息 001
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfsjxx")
    JsonResultVo<Object> transferDataOfsjxx(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：运输车信息 002
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfyscxx")
    JsonResultVo<Object> transferDataOfyscxx(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：指派运输商 003
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfzpyss")
    JsonResultVo<Object> transferDataOfzpyss(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：指派运输商 003(3\4状态补传)
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfzpyss2")
    JsonResultVo<Object> transferDataOfzpyss2(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：取消指派运输商 004
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfqxzpyss")
    JsonResultVo<Object> transferDataOfqxzpyss(@RequestHeader("companyId") String companyId,
                                               @RequestHeader("custGroupId") String custGroupId,
                                               @RequestHeader("tenantId") String tenantId,
                                               @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：变更运输商 005
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfbgyss")
    JsonResultVo<Object> transferDataOfbgyss(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：打印提车单 006
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfdytcd")
    JsonResultVo<Object> transferDataOfdytcd(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：车辆位置信息 007
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfgpszt")
    JsonResultVo<Object> transferDataOfgpszt(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - VlmsToSpt：在途时间点 008
     */
    @PostMapping("/itf15VlmsToSpt/transferDataOfsjztsj")
    JsonResultVo<Object> transferDataOfsjztsj(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);

}
