package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 服务批量结算定时任务 服务批量结算，供应商批量结算
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SvcBatchFeignRemoteHystrix extends QmRemoteHystrix<SvcBatchFeignRemote> implements SvcBatchFeignRemote {

    @Override
    public JsonResultVo<Object> svcBatchTimingTask(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> vdrBatchTimingTask(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autoSvcInvc(String tenantId) {
        return getResult();
    }

    public JsonResultVo<Object> sendSvcInvc2FinV6(String tenantId, Map<String, String> map) {
        return getResult();
    }
}
