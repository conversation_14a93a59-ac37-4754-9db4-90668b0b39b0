package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 定时任务 - 融资余额自动转款
 */

@Component
@Slf4j
public class itfCopCarFallbackFactory implements FallbackFactory<itfCopCarRemote> {

    @Override
    public itfCopCarRemote create(Throwable throwable) {
        itfCopCarRemoteHystrix itfCopCarRemoteHystrix = new itfCopCarRemoteHystrix();
        itfCopCarRemoteHystrix.setHystrixEx(throwable);
        return itfCopCarRemoteHystrix;
    }
}
