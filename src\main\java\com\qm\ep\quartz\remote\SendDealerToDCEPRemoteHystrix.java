package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务 - 给DMS传输车籍信息(经销商信息同步)
 */
@Component
@Slf4j
public class SendDealerToDCEPRemoteHystrix extends QmRemoteHystrix<SendDealerToDCEPRemote> implements SendDealerToDCEPRemote {

    @Override
    public JsonResultVo SendDealerToDCEP(String tenantId, String companyId) {
        return null;
    }

    @Override
    public JsonResultVo salb021DailySettle(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendVehicleInfoToDCEP(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo salr052DailySettle(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo leadFromForDMS(String tenantId, String companyId, JsonParamDto dto) {
        return null;
    }


    @Override
    public JsonResultVo<Object> doSettle(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> findUserSynToSal(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> findPersonOrganizeSynToSal(String tenantId) {
        return getResult();
    }

}
