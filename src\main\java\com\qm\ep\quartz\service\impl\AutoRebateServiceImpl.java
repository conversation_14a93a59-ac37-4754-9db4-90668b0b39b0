package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.AutoRebateRemote;
import com.qm.ep.quartz.service.AutoRebateService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 形成销售返利服务
 *
 * <AUTHOR>
 * @date 2021/6/7 9:50
 */
@Service
public class AutoRebateServiceImpl implements AutoRebateService {
    @Autowired
    private AutoRebateRemote autoRebateRemote;

    /**
     * 形成销售返利
     *
     * @return
     */
    @Override
    public JsonResultVo calcSalRebate() {
        return autoRebateRemote.autoRebate("15");
    }

    /**
     * 自动更新建店支持额度状态
     *
     * @return
     */
    @Override
    public JsonResultVo<Boolean> setJDFinishState() {
        return autoRebateRemote.setJDFinishState("15");
    }


}
