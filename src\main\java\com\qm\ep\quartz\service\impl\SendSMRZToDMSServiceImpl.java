package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendSMRZToDMSService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendSMRZToDMSServiceImpl implements SendSMRZToDMSService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo SendSMRZToDMS() {
        return customRemote.sendSMRZToDMS("15");
    }

    @Override
    public JsonResultVo SendZXQSMRZToDMS() {
        return customRemote.sendZXQSMRZToDMS("15");
    }

}
