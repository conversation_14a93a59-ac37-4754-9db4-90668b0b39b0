package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class UcmsIscFeignRemoteHystrix extends QmRemoteHystrix<UcmsIscFeignRemote> implements UcmsIscFeignRemote {

    /**
     * 第一车网品牌数据接口（商用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarRootSY(String tenantId) {
        return getResult();
    }

    /**
     * 第一车网厂商车系数据接口（商用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarBrandSeriesSY(String tenantId) {
        return getResult();
    }

    /**
     * 第一车网车型数据接口（商用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarTypeSY(String tenantId) {
        return getResult();
    }

    /**
     * 第一车网品牌数据接口（乘用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarRootCY(String tenantId) {
        return getResult();
    }

    /**
     * 第一车网厂商车系数据接口（乘用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarBrandSeriesCY(String tenantId) {
        return getResult();
    }

    /**
     * 第一车网车型数据接口（乘用车）
     * add by zhoul
     */
    @Override
    public JsonResultVo getIautosCarTypeCY(String tenantId) {
        return getResult();
    }

    /**
     * 获取旧车厂商
     * add by zhoul
     */
    @Override
    public JsonResultVo getOCarBrand(String tenantId) {
        return getResult();
    }

    /**
     * 获取旧车车系
     * add by zhoul
     */
    @Override
    public JsonResultVo getOCarSeries(String tenantId) {
        return getResult();
    }

    /**
     * 获取旧车车型
     * add by zhoul
     */
    @Override
    public JsonResultVo getOCarType(String tenantId) {
        return getResult();
    }

}
