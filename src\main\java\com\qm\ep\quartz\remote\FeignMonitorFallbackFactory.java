package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class FeignMonitorFallbackFactory implements FallbackFactory<FeignMonitorRemote> {

    @Override
    public FeignMonitorRemote create(Throwable throwable) {
        FeignMonitorRemoteHystrix feignRemoteHystrix = new FeignMonitorRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
