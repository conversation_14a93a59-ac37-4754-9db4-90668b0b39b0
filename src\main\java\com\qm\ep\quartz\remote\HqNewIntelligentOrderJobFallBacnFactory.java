package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class HqNewIntelligentOrderJobFallBacnFactory implements FallbackFactory<HqNewIntelligentOrderJobRemote> {
    @Override
    public HqNewIntelligentOrderJobRemote create(Throwable throwable) {
        HqNewIntelligentOrderJobHystrix hqNewIntelligentOrderJobHystrix = new HqNewIntelligentOrderJobHystrix();
        hqNewIntelligentOrderJobHystrix.setHystrixEx(throwable);
        return hqNewIntelligentOrderJobHystrix;
    }
}
