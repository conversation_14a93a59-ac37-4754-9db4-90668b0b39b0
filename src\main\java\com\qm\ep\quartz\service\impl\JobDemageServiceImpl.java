package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.DemageRemote;
import com.qm.ep.quartz.service.JobDemageService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class JobDemageServiceImpl implements JobDemageService {

    @Autowired
    private DemageRemote demageRemote;

    @Override
    public JsonResultVo demageService() {
        return demageRemote.generate("15");
    }

}
