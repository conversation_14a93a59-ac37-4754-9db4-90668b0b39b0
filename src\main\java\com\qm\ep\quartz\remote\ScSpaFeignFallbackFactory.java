package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ScSpaFeignFallbackFactory implements FallbackFactory<ScSpaFeignRemote> {

    @Override
    public ScSpaFeignRemote create(Throwable throwable) {
        ScSpaFeignRemoteHystrix feignRemoteHystrix = new ScSpaFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
