package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ChandaoApiRemote;
import com.qm.ep.quartz.service.ChandaoApiService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ChaodaoApiServiceImpl implements ChandaoApiService {
    @Autowired
    private ChandaoApiRemote  chandaoApiRemote;

    @Override
    public JsonResultVo toUpdateAll() {
        return chandaoApiRemote.toUpdateAll();
    }
}
