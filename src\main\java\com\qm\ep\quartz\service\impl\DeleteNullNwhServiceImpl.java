package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.DeleteNullNwhRemote;
import com.qm.ep.quartz.service.DeleteNullNwhService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeleteNullNwhServiceImpl implements DeleteNullNwhService {

    @Autowired
    private DeleteNullNwhRemote deleteNullNwhRemote;

    @Override
    public JsonResultVo deleteNullNwh() {
        return deleteNullNwhRemote.generate("15");
    }
}
