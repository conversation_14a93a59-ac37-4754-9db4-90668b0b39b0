package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.GenerateAutoTransferInfoRemote;
import com.qm.ep.quartz.service.GenerateAutoTransferInfoService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @CreateTime 2020-09-30 8:44
 * @Version 1.0
 */
@Service
public class GenerateAutoTransferInfoServiceImpl implements GenerateAutoTransferInfoService {

    @Autowired
    private GenerateAutoTransferInfoRemote generateAutoTransferInfoRemote;

    @Override
    public JsonResultVo generateAutoTransferData() {
        return generateAutoTransferInfoRemote.generateAutoTransferData("15");
    }

    @Override
    public JsonResultVo deleteTemp() {
        return generateAutoTransferInfoRemote.deleteTemp("15");
    }

    @Override
    public JsonResultVo autoDistributeByQuartz() {
        return generateAutoTransferInfoRemote.autoDistributeByQuartz("15");

    }

    @Override
    public JsonResultVo syncSaleInvoiceToV6() {
        return generateAutoTransferInfoRemote.syncSaleInvoiceToV6("15");
    }
}
