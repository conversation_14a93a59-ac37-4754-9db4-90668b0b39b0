package com.qm.ep.quartz.service.impl;


import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.service.autoFormOrderService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class autoFormOrderServiceImpl implements autoFormOrderService {

    @Autowired
    com.qm.ep.quartz.remote.itftosalRemote itftosalRemote;

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }



    @Override
    public JsonResultVo<Boolean> autoFormOrder(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.autoFormOrder(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> autoFormOrderIwork(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.autoFormOrderIwork(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb008e(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.autoFormOrderForSalb008e(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb23503(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.autoFormOrderForSalb23503(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }


    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb23507(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.autoFormOrderForSalb23507(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> offlineDing(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.offlineDing(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    @Override
    public JsonResultVo<Boolean> offlineBeforeTwoDays(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.offlineBeforeTwoDays(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

}
