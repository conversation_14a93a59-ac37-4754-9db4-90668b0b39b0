package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */
@Component
@Slf4j
@Data
public class DeleteNullNwhRemoteHystrix extends QmRemoteHystrix<DeleteNullNwhRemote> implements DeleteNullNwhRemote {

    @Override
    public JsonResultVo<Object> generate(String tenantId) {
        return getResult();
    }
}
