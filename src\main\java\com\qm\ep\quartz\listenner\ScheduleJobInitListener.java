package com.qm.ep.quartz.listenner;


import com.qm.ep.quartz.service.JobService;
import com.qm.ep.quartz.utils.QuartzManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Order(value = 1)
public class ScheduleJobInitListener implements CommandLineRunner {
    @Autowired
    JobService scheduleJobService;

    @Autowired
    QuartzManager quartzManager;

    @Override
    public void run(String... arg0) throws Exception {
        try {
            scheduleJobService.initSchedule();
        } catch (Exception e) {
            log.info("---error--context", e);
        }

    }
}
