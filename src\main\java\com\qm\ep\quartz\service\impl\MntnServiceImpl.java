package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcMntnFeignRemote;
import com.qm.ep.quartz.service.MntnService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class MntnServiceImpl implements MntnService {

    @Autowired
    private SvcMntnFeignRemote svcMntnFeignRemote;

    @Override
    public JsonResultVo mntnService(String params) {
        Map<String,String> map=new HashMap<>();
        map.put("limitCount",params);
        return svcMntnFeignRemote.mntnAudit("15",map);
    }
}