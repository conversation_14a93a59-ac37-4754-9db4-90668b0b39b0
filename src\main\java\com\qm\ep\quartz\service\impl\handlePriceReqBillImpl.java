package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.handlePriceReqBillService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class handlePriceReqBillImpl implements handlePriceReqBillService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * 处理过期价格申请单
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo handleData() {
        return financeTransferRemote.handleData("15", "6000");
    }
}
