package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * l
 *
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = FinanceTransferFallbackFactory.class)
public interface itfCopCarRemote {

    /**
     *移动出行销售车辆信息接口
     */
    @PostMapping("/interfacecop/sendCopSellCarInfo")
    JsonResultVo sendCopSellCarInfo(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);

    /**
     *移动出行退车车辆信息接口
     */
    @PostMapping("/interfacecop/sendCopReturnCarInfo")
    JsonResultVo sendCopReturnCarInfo(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);

}
