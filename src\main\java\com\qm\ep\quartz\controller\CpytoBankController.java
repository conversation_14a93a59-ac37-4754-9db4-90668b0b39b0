package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.service.TdsServiceCpyService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author: zyz
 * @time: 2021/4/2 14:17
 */
@Tag(name = "推送三方融资定时任务使用", description = "推送三方融资定时任务使用")
@RestController
@RequestMapping("/cpy")
public class CpytoBankController extends BaseController {
    @Autowired
    TdsServiceCpyService tdsServiceCpyService;

    /**
     * 定时任务 - 推送银行消息
     */
    @Operation(summary = "推送银行消息", description = "推送银行消息[author:********]")
    @GetMapping("/sendBankMessage")
    public JsonResultVo sendBankMessage(@RequestParam String bankCode) {
        return tdsServiceCpyService.sendBankMessage(bankCode);
    }

    /**
     * 定时任务 - 单独推送银行消息
     */
    @Operation(summary = "推送银行消息", description = "推送银行消息[author:********]")
    @GetMapping("/sendBankMessage80")
    public JsonResultVo sendBankMessage80(@RequestParam String bankCode, @RequestParam String tranfunc) {
        return tdsServiceCpyService.sendBankMessage80(bankCode, tranfunc);
    }

    /**
     * 定时任务 - 推送eom融资申请单
     */
    @Operation(summary = "推送eom融资申请单", description = " 推送eom融资申请单[author:********]")
    @PostMapping("/pushCd")
    public JsonResultVo pushCd() {
        return tdsServiceCpyService.pushCd();
    }

    /**
     * 定时任务 - V推送eom融资申请
     */
    @Operation(summary = "推送eom融资申请", description = "推送eom融资申请[author:********]")
    @PostMapping("/pushF1f2")
    public JsonResultVo pushF1f2() {
        return tdsServiceCpyService.pushF1f2();
    }
}
