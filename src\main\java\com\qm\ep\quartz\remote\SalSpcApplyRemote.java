package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = SalSpcApplyFallbackFactory.class)
public interface SalSpcApplyRemote {
    /**
     * 定时任务执行 无效报备单作废
     */
    @PostMapping("/salb171/jobVoid")
    JsonResultVo<Object> voidSpcApply(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    @PostMapping("/dealerBranch/updateMdac100d2")
    JsonResultVo<Boolean> updateMdac100d2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
