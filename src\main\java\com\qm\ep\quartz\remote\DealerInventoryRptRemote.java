package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 经销商库存和提车在途日报生成
 * <AUTHOR>
 * @date 2021/7/9 10:53
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = DealerInventoryRptFallbackFactory.class)
public interface DealerInventoryRptRemote {
    @PostMapping("/salr024/createInventoryRpt")
    JsonResultVo createInventoryRpt(@RequestHeader("tenantId") String tenantId);
}
