package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.domain.dto.MessageRunTimeJobDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ly add 20210701
 */
@Component
@Slf4j
@Data
public class FeignTempleRemoteHystrix extends QmRemoteHystrix<FeignTempleRemote> implements FeignTempleRemote {
    @Override
    public JsonResultVo<List<MessageRunTimeJobPO>> sendNoticeJob(String tenantId, String companyId, String userId) {
        return getResult();
    }

    @Override
    public JsonResultVo<List<MessageRunTimeJobPO>> getMessageRunTimeJobListForRun(String tenantId, MessageRunTimeJobDTO messageRunTimeJobDTO) {
        return getResult();
    }
}
