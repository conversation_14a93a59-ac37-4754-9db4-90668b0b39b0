package com.qm.ep.quartz.service.impl;
import com.qm.ep.quartz.remote.NewxbcxsqdRemote;
import com.qm.ep.quartz.service.NewxbcxsqdService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class NewxbcxsqdServiceImpl implements NewxbcxsqdService {
    @Autowired
    private NewxbcxsqdRemote newxbcxsqdRemote;

    /**
     * 处理新保促销申请单状态
     * add by zjc
     */
    @Override
    public JsonResultVo btsalb717() {
        return newxbcxsqdRemote.btsalb717("15");
    }

    /**
     * 处理续保促销申请单状态
     * add by zjc
     */
    @Override
    public JsonResultVo btsalb720() {
        return newxbcxsqdRemote.btsalb720("15");
    }

    /**
     * 处理保险合伙人促销申请单状态
     * add by zjc
     */
    @Override
    public JsonResultVo btsalb731() {
        return newxbcxsqdRemote.btsalb731("15");
    }

}
