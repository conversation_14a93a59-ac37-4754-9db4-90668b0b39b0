package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */

@Component
@Slf4j
public class Itf15SptToTdsv2FallbackFactory implements FallbackFactory<Itf15SptToTdsv2Remote> {

    @Override
    public Itf15SptToTdsv2Remote create(Throwable throwable) {
        Itf15SptToTdsv2RemoteHystrix priceremotehystrix = new Itf15SptToTdsv2RemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
