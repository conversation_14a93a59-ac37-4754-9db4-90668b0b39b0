package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


@Repository
@FeignClient(name = "sc-service-spa", fallbackFactory = ScSpaFeignFallbackFactory.class)
public interface ScSpaFeignRemote {

    /**
     * 定时任务执行 Fbom物料接口处理
     */
    @PostMapping("/fbomInterface/scheduleFbomMasterial")
    JsonResultVo<Object> scheduleFbomMasterial(@RequestHeader("tenantId") String tenantId);


    @PostMapping("/fbomInterface/scheduleFbomVehicleSpa")
    JsonResultVo<Object> scheduleFbomVehicleSpa(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务执行 Fbom获取产品与备件之间关系接口
     */
    @PostMapping("/fbomInterface/relationsSpaAndProduct")
    JsonResultVo<Object> relationsBetweenSpaAndProduct(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 自动把没维护备件索赔页的插入索赔页 by sunjie
     */
    @PostMapping("/spaClaimMsg/generate")
    JsonResultVo<Object> generate(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务执行 Fbom产品对应关系频次调用接口
     */
    @PostMapping("/fbomInterface/fbomProductTimesInterface")
    JsonResultVo<Object> fbomProductTimesInterface(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务执行 备件信息同步到tdsv2
     */
    @PostMapping("/spaSparePartsMainfile/syncToTdsV2")
    JsonResultVo<Object> syncToTdsV2(@RequestHeader("tenantId") String tenantId);
}
