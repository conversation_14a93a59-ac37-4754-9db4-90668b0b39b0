package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.AutoGenerateMonthReportDTO;
import com.qm.ep.quartz.domain.dto.AutoGenerateWeekReportDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
@Component
@FeignClient(name = "tds-service-spa-querysal",fallbackFactory =GeneratePartFinanceReportRemoteFallBackFactory.class)
public interface GeneratePartFinanceReportRemote {

    @PostMapping("/partFinanceReport/autoGenerateMonthReport")
    JsonResultVo generatPartMonthReport(@RequestHeader("tenantId") String tenantId,@RequestBody AutoGenerateMonthReportDTO paramsDTO);

    @PostMapping("/partFinanceReport/autoGenerateWeekReport")
    JsonResultVo generatPartWeekReport(@RequestHeader("tenantId") String tenantId,@RequestBody AutoGenerateWeekReportDTO paramsDTO);

    @PostMapping("/partFinanceReport/autoGenerateBeginEndDate")
    JsonResultVo autoGenerateBeginEndDate(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/spaPenetrationRate/generateSpaPenetrationRateInfo")
    JsonResultVo generateSpaPenetrationRateInfo(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/spaPenetrationRate/autoCal517")
    JsonResultVo autoCal517(@RequestHeader("tenantId") String tenantId);
}
