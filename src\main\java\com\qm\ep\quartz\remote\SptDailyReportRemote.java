package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = SptDailyReportFallbackFactory.class)
public interface SptDailyReportRemote {

    /**
     * 定时任务 - 生成公司库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @PostMapping("/sptDailyReport/insertSalb026DDO")
    JsonResultVo<Object> insertSalb026DDO(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);
    /**
     * 定时任务 - 生成在途明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @PostMapping("/sptDailyReport/insertSptb022DDO")
    JsonResultVo<Object> insertSptb022DDO(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);
    /**
     * 定时任务 - 生成经销商库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @PostMapping("/sptDailyReport/insertSalb027DDO")
    JsonResultVo<Object> insertSalb027DDO(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 使生效的运费转存到当前表(每日0：10左右生成)
     */
    @PostMapping("/sptDailyReport/transferCurrentSptc072")
    JsonResultVo<Object> transferCurrentSptc072(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);
}
