package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.UcmsSelFeignRemote;
import com.qm.ep.quartz.service.UcmsSelService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UcmsSelServiceImpl implements UcmsSelService {
    @Autowired
    private UcmsSelFeignRemote ucmsSelFeignRemote;

    /**
     * 定时任务自动下架
     * add by zhoul
     */
    @Override
    public JsonResultVo doOffAutoTask() {
        return ucmsSelFeignRemote.doOffAutoTask("15");
    }
}
