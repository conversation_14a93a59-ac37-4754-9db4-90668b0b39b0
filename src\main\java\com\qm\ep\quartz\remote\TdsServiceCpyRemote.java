package com.qm.ep.quartz.remote;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(name = "tds-service-cpy",fallbackFactory = TdsServiceCpyFallBackFactory.class)
public interface TdsServiceCpyRemote {
    @GetMapping("/bank/sendBankMessage")
    JsonResultVo sendBankMessage(@RequestParam String  bankCode);
    @GetMapping("/bank/sendBankMessage80")
    JsonResultVo sendBankMessage80(@RequestParam String  bankCode,String tranfunc);
    @GetMapping("/eom/pushcd")
    JsonResultVo pushCd();
    @GetMapping("/eom/pushf1f2")
    JsonResultVo pushF1f2();
}
