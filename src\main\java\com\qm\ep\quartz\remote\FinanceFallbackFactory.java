package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @ClassName : FinanceFallbackFactory
 * @Description :
 * <AUTHOR> WQS
 * @Date: 2021-03-27  9:56
 */
@Component
public class FinanceFallbackFactory implements FallbackFactory<FinanceRemote> {

    @Override
    public FinanceRemote create(Throwable throwable) {
        FinanceRemoteHystrix financeRemoteHystrix = new FinanceRemoteHystrix();
        financeRemoteHystrix.setHystrixEx(throwable);
        return financeRemoteHystrix;
    }
}
