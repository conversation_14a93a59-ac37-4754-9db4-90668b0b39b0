package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Component
@FeignClient(name = "tds-service-spa-manapur")
public interface HqNewIntelligentOrderJobRemote {

    @PostMapping("/hqNewIntelligentOrderJob/calculateStockPartMad")
    JsonResultVo calculateStockPartMad(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/hqNewIntelligentOrderJob/calculateStockWidth")
    JsonResultVo calculateStockWidth(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/hqNewIntelligentOrderJob/calculateStockPartRequir")
    JsonResultVo calculateStockPartRequir(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/hqNewIntelligentOrderJob/calculateStockPartParams")
    JsonResultVo calculateStockPartParams(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/hqNewIntelligentOrderJob/calculateStockThreeYearsSaleCount")
    JsonResultVo calculateStockThreeYearsSaleCount(@RequestHeader("tenantId") String tenantId);


}
