package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToVlmsFallbackFactory.class)
public interface Itf15SptToVlmsRemote {

    /**
     * 定时任务 - SptToVlms：省区 001
     */
    @PostMapping("/itf15SptToVlms/transferDataOfcsqdm")
    JsonResultVo<Object> transferDataOfcsqdm(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);

    /**
     * 定时任务 - SptToVlms：市 002
     */
    @PostMapping("/itf15SptToVlms/transferDataOfcsxdm")
    JsonResultVo<Object> transferDataOfcsxdm(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);

    /**
     * 定时任务 - SptToVlms：区县 003
     */
    @PostMapping("/itf15SptToVlms/transferDataOfcxqdm")
    JsonResultVo<Object> transferDataOfcxqdm(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);

    /**
     * 定时任务 - SptToVlms：产品代码 004
     */
    @PostMapping("/itf15SptToVlms/transferDataOfcpdm")
    JsonResultVo<Object> transferDataOfcpdm(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：发运计划 005
     */
    @PostMapping("/itf15SptToVlms/transferDataOffyjh")
    JsonResultVo<Object> transferDataOffyjh(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：删除发运计划 006
     */
    @PostMapping("/itf15SptToVlms/transferDataOftfyjh")
    JsonResultVo<Object> transferDataOftfyjh(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：指派车道 007
     */
    @PostMapping("/itf15SptToVlms/transferDataOfzpcd")
    JsonResultVo<Object> transferDataOfzpcd(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：取消指派车道 008
     */
    @PostMapping("/itf15SptToVlms/transferDataOfqxzpcd")
    JsonResultVo<Object> transferDataOfqxzpcd(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：发运出库 009
     */
    @PostMapping("/itf15SptToVlms/transferDataOffyck")
    JsonResultVo<Object> transferDataOffyck(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：出库换车 010
     */
    @PostMapping("/itf15SptToVlms/transferDataOfckhc")
    JsonResultVo<Object> transferDataOfckhc(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：车辆到货 011
     */
    @PostMapping("/itf15SptToVlms/transferDataOfcldh")
    JsonResultVo<Object> transferDataOfcldh(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：计划返单 012
     */
    @PostMapping("/itf15SptToVlms/transferDataOfjhfd")
    JsonResultVo<Object> transferDataOfjhfd(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - SptToVlms：运费结算 013
     */
    @PostMapping("/itf15SptToVlms/transferDataOfyfjs")
    JsonResultVo<Object> transferDataOfyfjs(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);

    /**
     * 定时任务 - SptToVlms：下线入库信息传给一汽物流接口
     */
    @PostMapping("/itf15SptToWms/transferDataOfWms")
    JsonResultVo<Object> transferDataOfWms(@RequestHeader("companyId") String companyId,
                                           @RequestHeader("custGroupId") String custGroupId,
                                           @RequestHeader("tenantId") String tenantId,
                                           @RequestHeader("userId") String userId);


}
