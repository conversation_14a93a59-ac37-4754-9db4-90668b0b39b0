package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ChandaoApiRemote;
import com.qm.ep.quartz.service.ClearESIndexService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * ClassName:ClearESIndexServiceImpl
 * package:com.qm.ep.quartz.service.impl
 * Description:
 *
 * @Date:2022/2/21 14:06
 * @Author:xuxiaoliang
 */

@Slf4j
@Service
public class ClearESIndexServiceImpl implements ClearESIndexService {

    @Autowired
    private ChandaoApiRemote chandaoApiRemote;


    /**
     * 定时任务-清除elasticsearch过期索引
     */
    @Override
    public JsonResultVo clearEsIndex(String specialServiceName) {
        return chandaoApiRemote.clearEsIndex(specialServiceName);
    }
}
