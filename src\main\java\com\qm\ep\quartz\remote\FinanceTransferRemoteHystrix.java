package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务 - 融资余额自动转款
 */
@Component
@Slf4j
public class FinanceTransferRemoteHystrix extends QmRemoteHystrix<FinanceTransferRemote> implements FinanceTransferRemote {
    @Override
    public JsonResultVo<Object> financeTransfer(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendBillToV2(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> handleData(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> saveMarketPrice(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendDingMessage(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendSalb080ToV2(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> saveYjvoid(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendVehicleInfoForTsp(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendfailedVehicleInfoForTsp(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendDealerForTsp(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> transferSalb060ToTdsv2(String tenantId, String companyID,String batchSize) {
        return getResult();
    }

    @Override
    public JsonResultVo testDriveApplySubmitHandle(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo testDriveApplyAggregationHandle(String tenantId, String companyId) {
        return getResult();
    }
}
