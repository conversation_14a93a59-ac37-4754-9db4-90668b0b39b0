package com.qm.ep.quartz.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Data
@TableName("task_quartz_log")
@Schema(title = "定时任务日志", description = "定时任务日志")
public class QuartzLogDO implements Serializable {

    private static final long serialVersionUID = -349020555768861575L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(title = "id", description = "id")
    private String id;

    /**
     * job名称
     */
    @TableField("JobName")
    @Schema(title = "job名称", description = "job名称")
    private String JobName;

    @TableField("JobGroup")
    @Schema(title = "JobGroup", description = "JobGroup")
    private String JobGroup;

    /**
     * job开始时间
     */
    @Schema(title = "job开始时间", description = "job开始时间")
    @TableField("JobStartTime")
    private String JobStartTime;

    /**
     * job结束时间
     */
    @Schema(title = "job结束时间", description = "job结束时间")
    @TableField("JobEndTime")
    private String JobEndTime;

    /**
     * job运行时间
     */
    @Schema(title = "job运行时间", description = "job运行时间")
    @TableField("JobRunningTime")
    private String JobRunningTime;

    /**
     * job状态
     */
    @Schema(title = "job状态", description = "job状态")
    @TableField("JobStatus")
    private String JobStatus;

    /**
     * job状态信息
     */
    @Schema(title = "job状态信息", description = "job状态信息")
    @TableField("JobStatusMes")
    private String JobStatusMes;

    /**
     * 时间戳
     */
    @Version
    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;
}
