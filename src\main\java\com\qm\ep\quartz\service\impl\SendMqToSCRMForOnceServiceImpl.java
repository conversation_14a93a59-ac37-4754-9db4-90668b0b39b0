package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendMqToSCRMForOnceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendMqToSCRMForOnceServiceImpl implements SendMqToSCRMForOnceService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendMqToSCRMForOnce() {
        return customRemote.sendMqToSCRMForOnce("15","6000");
    }

}
