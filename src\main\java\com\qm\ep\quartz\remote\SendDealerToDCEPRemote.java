package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 给DMS传输车籍信息(经销商信息同步)
 *
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = SendDealerToDCEPFallbackFactory.class)
public interface SendDealerToDCEPRemote {

    /**
     * 定时任务 - 给DMS传输车籍信息(经销商信息同步)
     */
    @PostMapping("/interface/sendDealerToDCEP")
    JsonResultVo SendDealerToDCEP(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 向dcep传车籍信息(车籍信息同步接口)
     */
    @PostMapping("/interface/sendVehicleInfoToDCEP")
    JsonResultVo sendVehicleInfoToDCEP(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - salb021日结
     */
    @PostMapping("/sALB021/dailySettle")
    JsonResultVo salb021DailySettle(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 接收DMS线索来源数据
     */
    @PostMapping("/leadFrom/getList")
    JsonResultVo leadFromForDMS(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId, @RequestBody JsonParamDto dto);

    /**
     * 定时任务 - salr052日结
     */
    @PostMapping("/salr052/salr052DailySettle")
    JsonResultVo salr052DailySettle(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 销售月结
     */
    @PostMapping("/salUtil/doSettle")
    JsonResultVo doSettle(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - SAL库人员信息同步
     */
    @PostMapping("/sysc030/findUserSynToSal")
    JsonResultVo findUserSynToSal(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - SAL库人员组织信息同步
     */
    @PostMapping("/sysc033/findPersonOrganizeSynToSal")
    JsonResultVo findPersonOrganizeSynToSal(@RequestHeader("tenantId") String tenantId);


}
