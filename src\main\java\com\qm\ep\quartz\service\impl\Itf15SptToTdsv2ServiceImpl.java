package com.qm.ep.quartz.service.impl;


import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToTdsv2Remote;
import com.qm.ep.quartz.service.Itf15SptToTdsv2Service;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jianghong
 * @time: 2021/4/22 18:30
 */
@Service
public class Itf15SptToTdsv2ServiceImpl implements Itf15SptToTdsv2Service {
    @Autowired
    Itf15SptToTdsv2Remote itf15SptToTdsv2Remote;

    /**
     * 定时任务 - 001 SptToTdsv2：传输运费结算单数据
     */
    @Override
    public JsonResultVo transferDataOfSptb070CD(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataOfSptb070CD(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 002 SptToTdsv2：插入采购结算池数据(异步)
     */
    @Override
    public JsonResultVo transferDataOfInsertSalb046(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataOfInsertSalb046(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务v6 - 002 SptToTdsv2：插入采购结算池数据(异步)
     */
    @Override
    public JsonResultVo transferDataOfInsertSalb046v6(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataOfInsertSalb046v6(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 003 SptToTdsv2：删除采购结算池数据(异步)
     */
    @Override
    public JsonResultVo transferDataOfDeleteSalb046(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataOfDeleteSalb046(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务v6 - 003 SptToTdsv2：删除采购结算池数据(异步)
     */
    @Override
    public JsonResultVo transferDataOfDeleteSalb046v6(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataOfDeleteSalb046v6(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    @Override
    public JsonResultVo transferData(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferData(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    @Override
    public JsonResultVo transferDatav6(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDatav6(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 提车出库  新能源 spt传给fin 补传
     */
    @Override
    public JsonResultVo transferDataRepair(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToTdsv2Remote.transferDataRepair(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }


    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
