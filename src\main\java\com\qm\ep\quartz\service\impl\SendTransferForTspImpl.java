package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendTransferForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendTransferForTspImpl implements SendTransferForTspService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo<String> sendnewVehicleTransferForTSP() {
        return customRemote.sendnewVehicleTransferForTSP("15", "6000");
    }

    @Override
    public JsonResultVo<String> sendnewfailedVehicleTransferForTSP() {
        return customRemote.sendnewfailedVehicleTransferForTSP("15", "6000");
    }


}
