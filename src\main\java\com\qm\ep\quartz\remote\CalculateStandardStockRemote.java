package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 -  备件模块自动计算季节件+非季节
 */
@Repository
@FeignClient(name = "tds-service-spa-manasal", fallbackFactory = CalculateStandardStockFallbackFactory.class)
public interface CalculateStandardStockRemote {


    /**
     * 定时任务 - 自动生成价格
     */
    @PostMapping("/spaCalculateStandardStock/startScheduleBySSQ")
    JsonResultVo<Object> startScheduleBySSQ(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 自动分货 --正常件
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribNormal")
    JsonResultVo<Object> autoDistribNormal(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 自动分货 --紧急订单
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribUrgent")
    JsonResultVo<Object> autoDistribUrgent(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 自动分货 --文创订单
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribCulturalCreation")
    JsonResultVo<Object> autoDistribCulturalCreation(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 超期欠货订单作废
     */
    @PostMapping("/spaCalculateStandardStock/autoabolish")
    JsonResultVo<Object> autoabolish(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务--库存销售日报表 calculateSpar925
     */
    @PostMapping("/spaCalculateStandardStock/dailyInventorySalesReport")
    JsonResultVo<Object> calculateSpar925(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 定时批量结算
     */
    @GetMapping("/spaInvcBillIndexMain/timedTask")
    JsonResultVo<Object> timedTask(@RequestHeader("tenantId") String tenantId);

    /**
     * 获取系统参数
     */
    @GetMapping("/spaInvcBillIndexMain/paravalue")
    JsonResultVo<Object> paravalue(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 在库品种合理率
     */
    @PostMapping("/dmsInterface/autoqueryrate")
    JsonResultVo<Object> autoqueryrate(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 续保
     */
    @PostMapping("/dmsInterface/autoRenewRatio")
    JsonResultVo<Object> autoRenewRatio(@RequestHeader("tenantId") String tenantId, @RequestHeader("date") String date);

    /**
     * 定时任务 - 大咖支援
     */
    @PostMapping("/dmsInterface/autoServiceSupport")
    JsonResultVo<Object> autoServiceSupport(@RequestHeader("tenantId") String tenantId, @RequestHeader("date") String date);

    /**
     * 定时任务 - 一次性修复率
     */
    @PostMapping("/dmsInterface/autoFFVComplaint")
    JsonResultVo<Object> autoFFVComplaint(@RequestHeader("tenantId") String tenantId, @RequestHeader("date") String date);

    @PostMapping("/spaInterface/summary")
    JsonResultVo summary(@RequestHeader("tenantId") String tenantId);


    // 定时任务 AAK 参数年月 202101
    @PostMapping("/businessPolicyNewSub/aak")
    JsonResultVo<Object> getAak(@RequestHeader("tenantId") String tenantId, @RequestHeader("date") String date);


    @PostMapping("/businessPolicyNewSub/recording")
    JsonResultVo<Object> getRecording(@RequestHeader("tenantId") String tenantId, @RequestHeader("date") String date);

    /**
     * 定时任务 - 自动分货 --新 2小时一次
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribCulturalNew")
    JsonResultVo<Object> autoDistribCulturalNew(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 自动分货 --新 提交 半小时一次
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribCulturalNew2")
    JsonResultVo<Object> autoDistribCulturalNew2(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 自动分货 --新 22点
     */
    @PostMapping("/spaCalculateStandardStock/autoDistribCulturalNew22")
    JsonResultVo<Object> autoDistribCulturalNew22(@RequestHeader("tenantId") String tenantId);

}
