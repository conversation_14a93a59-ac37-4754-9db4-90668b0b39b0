package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = SptCheckDataFallbackFactory.class)
public interface SptCheckDataRemote {

    /**
     * 定时任务 - 处理salb021库存数
     */
    @PostMapping("/sptCheckData/dealNinventoryOfSalb021")
    JsonResultVo<Object> dealNinventoryOfSalb021(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 处理sptb023库存数
     */
    @PostMapping("/sptCheckData/dealNinventoryOfSptb023")
    JsonResultVo<Object> dealNinventoryOfSptb023(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 处理sptb023分配库
     */
    @PostMapping("/sptCheckData/dealAssignOfSptb023")
    JsonResultVo<Object> dealAssignOfSptb023(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 同步sptc001
     */
    @PostMapping("/sptc001/updateSptc001")
    JsonResultVo<Boolean> updateSptc001(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
