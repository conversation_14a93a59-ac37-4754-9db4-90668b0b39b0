package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface UcmsIscService {

    /**
     * 第一车网品牌数据接口（商用车）
     */
    JsonResultVo getIautosCarRootSY();

    /**
     * 第一车网厂商车系数据接口（商用车）
     */
    JsonResultVo getIautosCarBrandSeriesSY();

    /**
     * 第一车网车型数据接口（商用车）
     */
    JsonResultVo getIautosCarTypeSY();

    /**
     * 第一车网品牌数据接口（乘用车）
     */
    JsonResultVo getIautosCarRootCY();

    /**
     * 第一车网厂商车系数据接口（乘用车）
     */
    JsonResultVo getIautosCarBrandSeriesCY();

    /**
     * 第一车网车型数据接口（乘用车）
     */
    JsonResultVo getIautosCarTypeCY();

    /**
     * 获取旧车厂商
     */
    JsonResultVo getOCarBrand();

    /**
     * 获取旧车车系
     */
    JsonResultVo getOCarSeries();

    /**
     * 获取旧车车型
     */
    JsonResultVo getOCarType();
}
