package com.qm.ep.quartz.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Schema(title = "发件箱定时任务日志", description = "发件箱定时任务日志")
@Data
public class MessageRunTimeJobDTO extends JsonParamDto {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    /**
     * dlm_message_notice id
     */
    private String nMainID;
    /**
     * 是否已经执行
     */
    private String isRun;
    /**
     * 发送时间
     */
    private Date dSend;

    private String createBy;

    private String createByName;

    private Date createOn;

    private String updateBy;

    private Long updateOn;

    private Integer recordVersion;

    private String nCompanyId;

    private Timestamp dtstamp;

    private Integer nCode;

    private Integer hhmmss;
}
