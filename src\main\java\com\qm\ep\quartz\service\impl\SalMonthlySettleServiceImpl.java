package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendDealerToDCEPRemote;
import com.qm.ep.quartz.service.SalMonthlySettleService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SalMonthlySettleServiceImpl implements SalMonthlySettleService {

    @Autowired
    private SendDealerToDCEPRemote sendDealerToDCEPRemote;

    @Override
    public JsonResultVo salMonthlySettle() {
        return sendDealerToDCEPRemote.doSettle("15");
    }

    @Override
    public JsonResultVo findUserSynToSal() {
        return sendDealerToDCEPRemote.findUserSynToSal("15");
    }

    @Override
    public JsonResultVo findPersonOrganizeSynToSal() {
        return sendDealerToDCEPRemote.findPersonOrganizeSynToSal("15");
    }
}
