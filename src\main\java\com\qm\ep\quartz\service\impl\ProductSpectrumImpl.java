package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.SendProductInfoRemote;
import com.qm.ep.quartz.service.ProductSpectrumService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProductSpectrumImpl implements ProductSpectrumService {

    @Autowired
    private SendProductInfoRemote sendProductInfoRemote;

    /**
     * 向fbom传送产品信息
     *
     * @Param: [tenantId, companyId]
     * author jiaorenmei
     * @Date:
     */
    @Override
    public JsonResultVo productSpectrum() {
        return sendProductInfoRemote.productSpectrum("15", "6000");
    }

    /**
     * 补充处理产品信息
     *
     * @Param: [tenantId, companyId]
     * author jiaorenmei
     * @Date:
     */
    @Override
    public JsonResultVo handleProductInfo(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return sendProductInfoRemote.handleProductInfo(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
    * @Description: 定时下发产品给三方接口
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/6/2
    */
    @Override
    public JsonResultVo<Object> sendPoructInfaceMdac008() {
        return sendProductInfoRemote.sendPoructInfaceMdac008("15", "6000");
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }


}
