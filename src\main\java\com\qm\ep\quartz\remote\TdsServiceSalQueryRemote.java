package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.InsertSalb063FiDTO;
import com.qm.ep.quartz.domain.dto.SearchPriceDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@Component
@FeignClient(name = "tds-service-sal-query",fallbackFactory = TdsServiceSalQueryFallBackFactory.class)
public interface TdsServiceSalQueryRemote {
    @PostMapping("/salb063fi/insertSalb063Fi")
    JsonResultVo insertSalb063Fi(@RequestHeader("tenantId") String tenantId, @RequestBody InsertSalb063FiDTO paramdDTO);

    @PostMapping("/searchPrice/tdsv6/tdsHistoryV2")
    JsonResultVo tdsHistoryV2(@RequestHeader("tenantId") String tenantId, @RequestBody SearchPriceDTO searchPriceDTO);


}
