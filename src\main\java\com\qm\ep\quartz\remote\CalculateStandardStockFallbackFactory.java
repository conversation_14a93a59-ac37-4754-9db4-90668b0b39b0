package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 计算ssq
 */

@Component
@Slf4j
public class CalculateStandardStockFallbackFactory implements FallbackFactory<CalculateStandardStockRemote> {

    @Override
    public CalculateStandardStockRemote create(Throwable throwable) {
        CalculateStandardStockRemoteHystrix priceremotehystrix = new CalculateStandardStockRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
