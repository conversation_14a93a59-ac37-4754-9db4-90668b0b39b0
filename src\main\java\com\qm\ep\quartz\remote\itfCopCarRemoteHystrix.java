package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务 - 融资余额自动转款
 */
@Component
@Slf4j
public class itfCopCarRemoteHystrix extends QmRemoteHystrix<itfCopCarRemote> implements itfCopCarRemote {

    @Override
    public JsonResultVo<Object> sendCopSellCarInfo(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendCopReturnCarInfo(String tenantId, String companyID) {
        return getResult();
    }

}
