package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendCustInfoUpdateForTSPService;
import com.qm.ep.quartz.service.SendVehicleTransferForTSPService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendCustInfoUpdateForTSPServiceImpl implements SendCustInfoUpdateForTSPService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendCustInfoUpdateForTSP() {
        return customRemote.sendCustInfoUpdateForTSP("15");
    }

}
