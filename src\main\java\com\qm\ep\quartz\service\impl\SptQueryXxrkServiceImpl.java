package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.domain.dto.TempnbykDTO;
import com.qm.ep.quartz.remote.SptQueryXxrkRemote;
import com.qm.ep.quartz.remote.SptUpdatePdiRemote;
import com.qm.ep.quartz.service.SptQueryXxrkService;
import com.qm.ep.quartz.service.SptUpdatePdiService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 批量执行下线入库(做成定时任务)
 *
 * @author: zhangyanpeng
 * @time: 2021/5/6
 */
@Service
public class SptQueryXxrkServiceImpl implements SptQueryXxrkService {

    @Autowired
    SptQueryXxrkRemote sptQueryXxrkRemote;

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse1() {
        return sptQueryXxrkRemote.executeInWarehouse1("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse2() {
        return sptQueryXxrkRemote.executeInWarehouse2("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse3() {
        return sptQueryXxrkRemote.executeInWarehouse3("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse4() {
        return sptQueryXxrkRemote.executeInWarehouse4("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse5() {
        return sptQueryXxrkRemote.executeInWarehouse5("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse6() {
        return sptQueryXxrkRemote.executeInWarehouse6("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse7() {
        return sptQueryXxrkRemote.executeInWarehouse7("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse8() {
        return sptQueryXxrkRemote.executeInWarehouse8("15", "6000");
    }

    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse9() {
        return sptQueryXxrkRemote.executeInWarehouse9("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse10() {
        return sptQueryXxrkRemote.executeInWarehouse10("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse11() {
        return sptQueryXxrkRemote.executeInWarehouse11("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse12() {
        return sptQueryXxrkRemote.executeInWarehouse12("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse13() {
        return sptQueryXxrkRemote.executeInWarehouse13("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse14() {
        return sptQueryXxrkRemote.executeInWarehouse14("15", "6000");
    }
    /**
     * 批量执行下线入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> executeInWarehouse15() {
        return sptQueryXxrkRemote.executeInWarehouse15("15", "6000");
    }

// 内部移库出库

    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime1() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(1);
        tempnbykDTO.setEnd(1000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime2() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(1001);
        tempnbykDTO.setEnd(2000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime3() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(2001);
        tempnbykDTO.setEnd(3000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime4() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(3001);
        tempnbykDTO.setEnd(4000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime5() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(4001);
        tempnbykDTO.setEnd(5000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime6() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(5001);
        tempnbykDTO.setEnd(6000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime7() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(6001);
        tempnbykDTO.setEnd(7000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime8() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(7001);
        tempnbykDTO.setEnd(8000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime9() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(8001);
        tempnbykDTO.setEnd(9000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime10() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(9001);
        tempnbykDTO.setEnd(10000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime11() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(10001);
        tempnbykDTO.setEnd(11000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    } /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime12() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(11001);
        tempnbykDTO.setEnd(12000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime13() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(12001);
        tempnbykDTO.setEnd(13000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime14() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(13001);
        tempnbykDTO.setEnd(14000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveOutTime15() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(14001);
        tempnbykDTO.setEnd(15000);
        return sptQueryXxrkRemote.internalMoveOutTime("15", "6000",tempnbykDTO);
    }
// 内部移库入库
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime1() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(1);
        tempnbykDTO.setEnd(1000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime2() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(1001);
        tempnbykDTO.setEnd(2000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime3() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(2001);
        tempnbykDTO.setEnd(3000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime4() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(3001);
        tempnbykDTO.setEnd(4000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime5() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(4001);
        tempnbykDTO.setEnd(5000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime6() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(5001);
        tempnbykDTO.setEnd(6000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime7() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(6001);
        tempnbykDTO.setEnd(7000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime8() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(7001);
        tempnbykDTO.setEnd(8000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime9() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(8001);
        tempnbykDTO.setEnd(9000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime10() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(9001);
        tempnbykDTO.setEnd(10000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime11() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(10001);
        tempnbykDTO.setEnd(11000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime12() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(11001);
        tempnbykDTO.setEnd(12000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime13() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(12001);
        tempnbykDTO.setEnd(13000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    } /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime14() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(13001);
        tempnbykDTO.setEnd(1400);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    } /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> internalMoveInTime15() {
        TempnbykDTO tempnbykDTO=new TempnbykDTO();
        tempnbykDTO.setStart(14001);
        tempnbykDTO.setEnd(15000);
        return sptQueryXxrkRemote.internalMoveInTime("15", "6000",tempnbykDTO);
    }


}
