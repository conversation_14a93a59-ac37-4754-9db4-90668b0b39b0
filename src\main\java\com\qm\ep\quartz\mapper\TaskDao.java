package com.qm.ep.quartz.mapper;

import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.domain.vo.TaskVO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-10-03 15:45:42
 */
@Repository
@Mapper
public interface TaskDao extends QmBaseMapper<QuartzDO> {

    TaskVO get(String id);

    List<TaskVO> maplist(Map<String, Object> map);

    int count(Map<String, Object> map);

    int update(QuartzDO task);

    int remove(String id);


    int batchRemove(String[] ids);

    int saveJobLog(QuartzLogDO jobLogDo);

    void updateJobLog(QuartzLogDO jobLogDo);

    List<String> remoteGroupList();

    List<String> remoteTaskListByGroup(String vGroup);
}
