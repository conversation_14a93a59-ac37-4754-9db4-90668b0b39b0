package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SendSALB061ToV2Fallback implements FallbackFactory<SendSALB061ToV2Remote> {
    @Override
    public SendSALB061ToV2Remote create(Throwable throwable) {
        SendSALB061ToV2RemoteHystrix sendSALB061ToV2RemoteHystrix = new SendSALB061ToV2RemoteHystrix();
        sendSALB061ToV2RemoteHystrix.setHystrixEx(throwable);
        return sendSALB061ToV2RemoteHystrix;
    }
}
