package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-sys", fallbackFactory = PriceFallbackFactory.class)
public interface DemageRemote {

    /**
     * 定时任务 - 自动把没维护供应商服务页的插入服务页
     */
    @PostMapping("/demage/generate")
    JsonResultVo generate(@RequestHeader("tenantId") String tenantId);

}
