<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.quartz.mapper.QuartzMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.quartz.domain.bean.QuartzDO">
        <id column="id" property="id"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="method_name" property="methodName"/>
        <result column="is_concurrent" property="isConcurrent"/>
        <result column="description" property="description"/>
        <result column="update_by" property="updateBy"/>
        <result column="bean_class" property="beanClass"/>
        <result column="create_date" property="createDate"/>
        <result column="job_status" property="job_status"/>
        <result column="job_group" property="jobGroup"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="spring_bean" property="springBean"/>
        <result column="job_name" property="jobName"/>
        <result column="is_parallel" property="isParallel"/>
        <result column="job_para" property="jobPara"/>
        <result column="warning_flag" property="warningFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, cron_expression,create_by_name,update_by_name, method_name, is_concurrent, description, update_by, bean_class, create_date, job_status, job_group, update_date, create_by, job_para, spring_bean, job_name,is_parallel, dtstamp
    ,warningFlag
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
     select * from (
        select
            a.cron_expression as cronExpression,
            a.method_name as methodName,
            a.create_by_name as createByName,
            a.update_by_name as updateByName,
            a.is_concurrent as isConcurrent,
            a.description as description,
            a.update_by as updateBy,
            a.bean_class as beanClass,
            a.create_date as createDate,
            a.job_status,
            a.job_group as jobGroup,
            a.update_date as updateDate,
            a.create_by as createBy,
            a.spring_bean as springBean,
            a.job_name as jobName,
            a.dtstamp,
            a.is_parallel as isParallel,
            a.job_para as jobPara,
            a.id,
            a.WARNING_FLAG as warningFlag
        from task_quartz a
        )
        innerTable
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="statisticsTable" resultType="com.qm.ep.quartz.domain.vo.QuartzStatisticsVO">
        select JobName, count(1) as nqty
        from task_quartz_log
        where JobStartTime BETWEEN #{startTime} and #{endTime}
        <if test=" list != null ">
            <foreach collection="list" index="index" item="item" open="and JobStatus in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by JobName
        order by 2 desc
    </select>
    <select id="statisticsTableByQuery" resultType="com.qm.ep.quartz.domain.vo.QuartzStatisticsVO">
        select
        *
        FROM
        (
        select JobName, count(1) as nqty
        from task_quartz_log
        where JobStartTime BETWEEN #{startTime} and #{endTime}
        <if test=" list != null ">
            <foreach collection="list" index="index" item="item" open="and JobStatus in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by JobName
        order by 2 desc
        )innerTable
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectDistinctGroup" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        select DISTINCT(job_group) job_group from task_quartz
    </select>
    <select id="selectDistinctName" resultType="com.qm.ep.quartz.domain.bean.QuartzDO">
        select DISTINCT(job_name) job_name from task_quartz
    </select>
</mapper>
