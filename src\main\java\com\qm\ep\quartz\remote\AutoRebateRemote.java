package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 形成销售返利
 *
 * <AUTHOR>
 * @date 2021/6/7 9:52
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = AutoRebateFallbackFactory.class)
public interface AutoRebateRemote {
    /**
     * 定时任务 - 形成销售返利
     */
    @PostMapping("/salb245d/autoRebate")
    JsonResultVo autoRebate(@RequestHeader("tenantId") String tenantId);

    /**
     * 建店额度状态自动设置
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/salb244/setFinishState")
    JsonResultVo<Boolean> setJDFinishState(@RequestHeader("tenantId") String tenantId);
}
