package com.qm.ep.quartz.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.mapper.QuartzLogMapper;
import com.qm.ep.quartz.service.JobService;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.ep.quartz.service.QuartzService;
import com.qm.ep.quartz.utils.DdUtils;
import com.qm.ep.quartz.utils.QuartzUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Arrays;

@Slf4j
@Component
//@DisallowConcurrentExecution 设置为串行
public class WelcomeJob implements Job {
    @Autowired
    private JobService jobService;
    @Autowired
    private QuartzService quartzService;
    @Autowired
    private QuartzLogService logService;
    @Autowired
    private QuartzLogMapper logMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private I18nUtil i18nUtil;
    //定时任务是否推送到钉钉群开关
    @Value("${quartz.enabled:false}")
    private boolean quartzEnabled;

    @Value("${quartz.dd.url:}")
    private String ddUrl;

    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
//        boolean canRunJob = true;// 是否可以执行Job
        long startTime = System.currentTimeMillis();//作业开始时间
        long endTime = 0;// 作业结束时间
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String dateStr = dateformat.format(startTime);
        Trigger trigger = arg0.getTrigger();
        JobKey getJobKey = trigger.getJobKey();

        QuartzDO jobDo = null;
        QuartzLogDO jobLogDo = new QuartzLogDO(); // 日志
        // 判断上次Job是否执行完毕
        String jobName = null;
        String jobParams = null;
        String nameParam = null;
        String id = null;
        try {
            log.info("----jobName--------"+getJobKey.getName());
            nameParam = getJobKey.getName().split("_")[0];
            id = getJobKey.getName().split("_")[1];
        } catch (Exception ex) {
            log.error("解析job唯一信息失败！" + ex.getMessage(), ex);
        }
        try {
            // 查询Job配置信息
            jobDo = quartzService.getJobById(id);
            jobParams = jobDo.getJobPara();
            jobName = getJobKey.getGroup() + ":" + nameParam + "(" + jobParams + ")";
            /**
             * 如果设置了不可以重叠执行，并且上次Job正在运行中，那么后续不可以执行。
             *
             * 现将 同步OR异步操作，利用java多态的特性程序化修改
             */
//            if (!"1".equals(jobDo.getIsParallel()) && logService.isJobRunning(jobName)) {
//                // 查询上次job是否正在执行ing
//                canRunJob = false;
//            }
        } catch (Exception ex) {
            log.info("---error--查询job信息失败！" + ex.getMessage(), ex);
            jobDo = new QuartzDO();
        }
        // 组装日志数据
        jobLogDo.setJobName(jobName);
        jobLogDo.setJobStartTime(dateStr);
        jobLogDo.setJobGroup(getJobKey.getGroup());
        jobLogDo.setJobStatus(QuartzUtils.JOB_LOG_RUNNING);//初始化
        //插入开始执行日志。需要独立事务
        logService.saveLog(jobLogDo);


        log.debug("开始执行作业：" + jobLogDo.getJobName() + "，参数：" + arg0);

        // 开始执行Job
        try {
            // 插入日志
//            if (canRunJob) {
            // 执行任务
            JsonResultVo<Object> d = callMethod(getJobKey.getGroup(), nameParam, jobParams);

            endTime = System.currentTimeMillis();    //获取结束时间
            String enddateStr = dateformat.format(endTime);
            log.info("执行作业完毕，耗时：" + (endTime - startTime) + "ms。" + JSON.toJSONString(d));
            jobLogDo.setJobEndTime(enddateStr);
            if (d != null) {
                jobLogDo.setJobStatus(d.getCode() + "");
                jobLogDo.setJobStatusMes(d.getMsg());
                if (!BootAppUtil.isNullOrEmpty(d.getMsg()) && d.getMsg().length() > 2000) {
                    String substring = d.getMsg().substring(0, 2000);
                    jobLogDo.setJobStatusMes(substring);
                }
            }
            // 记录执行时间
            jobLogDo.setJobRunningTime(String.valueOf(endTime - startTime));
            jobService.updateJobLog(jobLogDo);
            //定时任务本身执行失败，调用钉钉发送群消息通知--20220725--start--
            //判断是否为调用成功：200,  非200,发送钉钉消息推送
            log.info("调用返回状态："+d.getCode());
            if(!"200".equals(d.getCode())){
                String waring = jobDo.getWarningFlag();
                log.info("自动预警标识："+waring);//1:开启 ；  其他：不开启

                if(quartzEnabled && "1".equals(waring)){
                    //拼接发送内容
                    String content = "定时任务：定时任务本身执行失败，Remote任务："+jobDo.getJobName()+",任务描述："+jobDo.getDescription()
                            +"，开始时间："+dateStr+"，结束时间："+enddateStr +"，失败原因："+d.getMsg();
                    try {
                        String data = DdUtils.send(ddUrl, content);
                        JSONObject jsonObject = JSONObject.parseObject(data);
                        log.info("result="+jsonObject);
                        if(0 == Integer.parseInt(jsonObject.get("errcode").toString())){
                            log.info("推送成功！！！");
                        }
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                }
            }
            //定时任务本身执行失败，调用钉钉发送群消息通知--20220725--end--
        } catch (Exception e) {
            log.info("---error--执行作业失败！" + e.getMessage(), e);
            endTime = System.currentTimeMillis();    //获取结束时间
            String enddateStr = dateformat.format(endTime);
            jobLogDo.setJobEndTime(enddateStr);
            jobLogDo.setJobStatus(QuartzUtils.JOB_LOG_ERROR);
            String stackTrace = Arrays.toString(e.getStackTrace());
            String messageTemplate = "cause:%s,message:%s,stackTrace:%s";
            String cause = e.getCause() != null ? e.getCause().getMessage() : "";
            String message = e.getMessage() != null ? e.getMessage() : "";

            String format = String.format(messageTemplate, message, cause, message, stackTrace);

            jobLogDo.setJobStatusMes(format);
            if (format.length() > 2000) {
                String substring = format.substring(0, 2000);
                jobLogDo.setJobStatusMes(substring);
            }
            jobLogDo.setJobRunningTime(String.valueOf(endTime - startTime));
            jobService.updateJobLog(jobLogDo);
            //作业执行失败，调用钉钉发送群消息通知--20220725--start--
            String waring = jobDo.getWarningFlag();
            log.info("自动预警标识："+waring);//1:开启 ；  其他：不开启

            if(quartzEnabled && "1".equals(waring)){
                //拼接发送内容
                String content = "定时任务：执行作业失败，Remote任务："+jobDo.getJobName()+",任务描述："+jobDo.getDescription()
                        +"，开始时间："+dateStr+"，结束时间："+enddateStr+"，失败原因："+message;
                try {
                    String data = DdUtils.send(ddUrl, content);
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    log.info("result="+jsonObject);
                    if(0 == Integer.parseInt(jsonObject.get("errcode").toString())){
                        log.info("推送成功！！！");
                    }
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
            }
            //作业执行失败，调用钉钉发送群消息通知--20220725--end--

        }
    }

    /**
     * 通过反射查找作业任务函数并执行
     *
     * @param className  作业任务类名
     * @param methodName 作业任务函数名
     * @param jobParams  作业任务入参
     * @return 作业任务执行结果
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     * @throws ClassNotFoundException
     */
    private JsonResultVo<Object> callMethod(String className, String methodName, String jobParams) throws InvocationTargetException, IllegalAccessException, ClassNotFoundException {
        Class<?> jobClass = Class.forName(className);
        Object bean = applicationContext.getBean(jobClass);
        // 调用方法
        Method m = null;
        Method[] methods = jobClass.getDeclaredMethods();
        boolean isParam = false;// 是否有一个字符型入参
        for (Method method : methods) { // 找到所有该名子的方法（带参或无参）
            if (methodName.equals(method.getName())) {
                m = method;
                Class[] paramTypes = method.getParameterTypes();
                for (Class clazz : paramTypes) {
                    isParam = clazz.equals(String.class);
                    if (isParam) {
                        break;
                    }
                }
            }
        }
        if (m == null) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.welcomeJob.callMethodNonExist");
            throw new QmException(className + "#" + methodName + errorMsg);
        }
        JsonResultVo<Object> d = null;
        if (isParam) {
            d = (JsonResultVo) m.invoke(bean, jobParams);
        } else {
            d = (JsonResultVo) m.invoke(bean);
        }
        return d;
    }
}