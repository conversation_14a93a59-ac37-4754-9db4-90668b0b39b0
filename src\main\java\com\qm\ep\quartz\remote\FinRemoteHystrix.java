package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */
@Component
@Slf4j
public class FinRemoteHystrix extends QmRemoteHystrix<CustomRemote> implements FinRemote {

    @Override
    public JsonResultVo<List<Map<String, String>>> itf_ep_mdac008c() {
        return getResult();
    }
}
