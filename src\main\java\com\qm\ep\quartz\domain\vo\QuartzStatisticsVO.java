package com.qm.ep.quartz.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;


@Data
@Schema(title = "定时任务基本信息", description = "定时任务基本信息")
public class QuartzStatisticsVO implements Serializable {

    private static final long serialVersionUID = 7048335464359170301L;

    /**
     * 任务名
     */
    @Schema(title = "任务名", description = "任务名")
    private String jobName;

    @Schema(title = "次数", description = "次数")
    private String nqty;
}
