package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class SptUpdatePdiHystrix extends QmRemoteHystrix<SptUpdatePdiRemote> implements SptUpdatePdiRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Boolean> updatePDICheck(String tenantId, String companyId) {
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        String message = i18nUtil.getMessage("ERR.quartz.sptUpdatePdiHystrix.updatePDICheck");
        resultVo.setMsgErr(message);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> updateStatusBC(String tenantId){
        return getResult();
    }

}
