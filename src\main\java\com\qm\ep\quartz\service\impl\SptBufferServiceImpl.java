package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToMesRemote;
import com.qm.ep.quartz.remote.SptBufferRemote;
import com.qm.ep.quartz.service.Itf15SptToMesService;
import com.qm.ep.quartz.service.SptBufferService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: liuhao
 * @time: 2023/9/14 19:57
 */
@Service
public class SptBufferServiceImpl implements SptBufferService {

    @Autowired
    private SptBufferRemote bufferRemote;

    @Override
    public JsonResultVo<Boolean> autoLockVin(String loginKey) {
        LoginKeyDO loginKeyDO = this.getLoginKey(loginKey);
        return bufferRemote.autoLock(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> otdOrderMatchVin(String loginKey) {
        LoginKeyDO loginKeyDO = this.getLoginKey(loginKey);
        return bufferRemote.otdOrderMatchVin(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }

}
