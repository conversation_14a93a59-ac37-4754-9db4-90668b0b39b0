package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24
 */
@Service
public interface DataMonitorService {

    /**
     * 根据分组查询讯数据监控信息
     *
     * @param vgroup
     * @return
     */
    JsonResultVo monitorDataByVgroup1(String vgroup);
    JsonResultVo monitorDataByVgroup2(String vgroup);
    JsonResultVo monitorDataByVgroup3(String vgroup);
    JsonResultVo monitorDataByVgroup4(String vgroup);
    JsonResultVo monitorDataByVgroup5(String vgroup);
    JsonResultVo monitorDataByVgroup6(String vgroup);
    JsonResultVo monitorDataByVgroup7(String vgroup);
    JsonResultVo monitorDataByVgroup8(String vgroup);
    JsonResultVo monitorDataByVgroup9(String vgroup);
    JsonResultVo monitorDataByVgroup10(String vgroup);
    JsonResultVo monitorDataByVgroup11(String vgroup);
    JsonResultVo monitorDataByVgroup12(String vgroup);
    JsonResultVo monitorDataByVgroup13(String vgroup);
    JsonResultVo monitorDataByVgroup14(String vgroup);
    JsonResultVo monitorDataByVgroup15(String vgroup);
    JsonResultVo monitorDataByVgroup16(String vgroup);
    JsonResultVo monitorDataByVgroup17(String vgroup);
    JsonResultVo monitorDataByVgroup18(String vgroup);
    JsonResultVo monitorDataByVgroup19(String vgroup);
    JsonResultVo monitorDataByVgroup20(String vgroup);
}
