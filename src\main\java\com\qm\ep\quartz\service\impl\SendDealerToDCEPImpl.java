package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendDealerToDCEPRemote;
import com.qm.ep.quartz.service.SendDealerToDCEPService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendDealerToDCEPImpl implements SendDealerToDCEPService {

    @Autowired
    private SendDealerToDCEPRemote SendDealerToDCEPRemote;

    @Override
    public JsonResultVo SendDealerToDCEP() {
        return SendDealerToDCEPRemote.SendDealerToDCEP("15","6000");
    }


}
