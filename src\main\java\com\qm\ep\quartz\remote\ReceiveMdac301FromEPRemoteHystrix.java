package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName : V2接收EP客户信息
 * @Description :
 * <AUTHOR> wang<PERSON>
 * @Date: 2021-04-23  9:44
 */
@Slf4j
@Data
@Component
public class ReceiveMdac301FromEPRemoteHystrix extends QmRemoteHystrix<ReceiveMdac301FromEPRemote> implements ReceiveMdac301FromEPRemote {

    @Override
    public JsonResultVo<Boolean> receiveEPMdac301c(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> receiveEPMdac300(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> receiveEPMdac301d6(String tenantId) {
        return getResult();
    }
}
