package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.UTFinFeignRemote;
import com.qm.ep.quartz.service.UTFinService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UTFinServiceImpl implements UTFinService {
    @Autowired
    private UTFinFeignRemote utFinFeignRemote;

    static final String tenantId = "15";
    static final String companyId = "6000";

    /**
     * 更新贴息单状态FDTEditStatesInfo
     */
    @Override
    public JsonResultVo updateTxdState() {
        return utFinFeignRemote.updateTxdState(tenantId, companyId);
    }

    /**
     * 同步状态fdtStateJOB
     */
    @Override
    public JsonResultVo syncTxdStatus() {
        return utFinFeignRemote.syncTxdStatus(tenantId, companyId);
    }

    /**
     * 自动申请入账fdtPopJOB
     */
    @Override
    public JsonResultVo txdEnterAccount() {
        return utFinFeignRemote.txdEnterAccount(tenantId, companyId);
    }

    /**
     * 自动下发状态fdtDowStateJOB
     */
    @Override
    public JsonResultVo updateTxdTimestamp() {
        return utFinFeignRemote.updateTxdTimestamp(tenantId, companyId);
    }

    /**
     * 给DMS下发金融政策fdtInsPolicyInfo
     *
     * @return
     */
    @Override
    public JsonResultVo sendInsPolicyToDms() {
        return utFinFeignRemote.sendInsPolicyToDms(tenantId, companyId);
    }

    /**
     * 给DMS下发贴息单状态fdtYYDiscountDocStatesInfo
     *
     * @return
     */
    @Override
    public JsonResultVo sendTxdStateToDms() {
        return utFinFeignRemote.sendTxdStateToDms(tenantId, companyId);
    }

    @Override
    public JsonResultVo getInvoiceListUpdateDiscountDoc()  {
        return utFinFeignRemote.getInvoiceListUpdateDiscountDoc(tenantId, companyId);
    }

}
