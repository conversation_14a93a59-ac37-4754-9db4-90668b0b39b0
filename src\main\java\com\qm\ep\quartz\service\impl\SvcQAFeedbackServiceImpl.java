package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcQAFeedbackBillFeignRemote;
import com.qm.ep.quartz.service.SvcQAFeedbackService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SvcQAFeedbackServiceImpl implements SvcQAFeedbackService {
    @Autowired
    private SvcQAFeedbackBillFeignRemote feignRemote ;

    @Override
    public JsonResultVo auditQAFeedBack() {
        return feignRemote.auditQAFeedBack("15");
    }

    @Override
    public JsonResultVo autoSpaTimingTask() {
        return feignRemote.autoSpaTimingTask("15");
    }
}

