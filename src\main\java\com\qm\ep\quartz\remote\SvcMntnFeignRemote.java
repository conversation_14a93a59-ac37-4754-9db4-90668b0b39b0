package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;


/**l
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "tds-service-svc-mntn", fallbackFactory = SvcMntnFeignFallbackFactory.class)
public interface SvcMntnFeignRemote {

    /**
     * 保养单自动机审
     */
    @PostMapping("/automntnaudit/mntnAudit")
    JsonResultVo mntnAudit(@RequestHeader("tenantId")String tenantId, @RequestBody Map<String, String> map);

    /**
     * 临时处理车辆保养档案记录svcl020错误数据
     * @param tenantId
     * @return
     */
    @PostMapping("/mntnRecord/dealMntnRecordData")
    JsonResultVo dealMntnRecordData(@RequestHeader("tenantId")String tenantId);

    /**
     * 定时任务 - 修补计算错误数据—未逐次保养
     */
    @PostMapping("/mntnRecord/repairMntnRecord")
    JsonResultVo repairMntnRecord(@RequestHeader("tenantId") String tenantId);

}
