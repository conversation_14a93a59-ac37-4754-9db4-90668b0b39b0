package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.sendDealerForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendDealerForTsp implements sendDealerForTspService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public JsonResultVo sendDealerForTsp() {
        return financeTransferRemote.sendDealerForTsp("15", "6000");
    }
}
