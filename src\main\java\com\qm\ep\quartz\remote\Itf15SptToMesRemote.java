package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToMesFallbackFactory.class)
public interface Itf15SptToMesRemote {

    /**
     * 定时任务 - 001 SptToMes：传输库存数据
     */
    @PostMapping("/itf15SptToMes/transferDataOfInventory")
    JsonResultVo<Object> transferDataOfInventory(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 002 SptToMes：蔚山1厂下线入库回传接口(恒展系统)
     */
    @PostMapping("/itf15SptToMes/transferToHzmes")
    JsonResultVo<Object> transferToHzmes(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 003 SptToMes：繁荣工厂下线入库回传接口
     */
    @PostMapping("/itf15SptToMes/transferToFrmom")
    JsonResultVo<Object> transferToFrmom(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 004 SptToMes：蔚山2工厂下线入库回传接口
     */
    @PostMapping("/itf15SptToMes/transferToWs2mom")
    JsonResultVo<Object> transferToWs2mom(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

}
