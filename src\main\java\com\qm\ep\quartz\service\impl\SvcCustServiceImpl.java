package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcCustFeignRemote;
import com.qm.ep.quartz.service.SvcCustService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SvcCustServiceImpl implements SvcCustService {
    @Autowired
    private SvcCustFeignRemote feignRemote ;

    @Override
    public JsonResultVo<Object> vehicleClassTimingTask1() {
        return feignRemote.vehicleClassTimingTask("15");
    }
}

