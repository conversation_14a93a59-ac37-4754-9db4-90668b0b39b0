package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class SptCheckDataRemoteHystrix extends QmRemoteHystrix<SptCheckDataRemote> implements SptCheckDataRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 处理salb021库存数
     */
    @Override
    public JsonResultVo<Object> dealNinventoryOfSalb021(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptCheckDataRemoteHystrix.dealNinventoryOfSalb021");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 处理sptb023库存数
     */
    @Override
    public JsonResultVo<Object> dealNinventoryOfSptb023(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptCheckDataRemoteHystrix.dealNinventoryOfSptb023");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 处理sptb023分配库
     */
    @Override
    public JsonResultVo<Object> dealAssignOfSptb023(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptCheckDataRemoteHystrix.dealAssignOfSptb023");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> updateSptc001(String tenantId, String companyId) {
        return getResult();
    }

}
