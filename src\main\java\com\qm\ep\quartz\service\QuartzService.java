package com.qm.ep.quartz.service;

import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.dto.MessageRunTimeJobDTO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.IQmBaseService;
import com.qm.tds.base.domain.LoginKeyDO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
public interface QuartzService extends IQmBaseService<QuartzDO> {
    public boolean saveOrUpdateWithHistory(QuartzDO query, LoginKeyDO loginKey);

    public List<QuartzDO> selectDistinctGroup(Map map);

    public List<QuartzDO> selectDistinctName(Map map);

    /**
     * 查询Job信息
     *
     * @param jobGroup  Job分组，即类名。
     * @param jobMethod Job名称，即函数名。
     * @return Job信息
     */
    QuartzDO getJob(String jobGroup, String jobMethod);


    List<QuartzStatisticsVO> executionStatistics(QuartzDTO dto);

    QmPage<QuartzStatisticsVO> executionStatisticsQuery(QuartzDTO dto);

    JsonResultVo<List<MessageRunTimeJobPO>> getMessageRunTimeJobListForRun(MessageRunTimeJobDTO messageRunTimeJobDTO);

    JsonResultVo<List<MessageRunTimeJobPO>> sendNoticeJob();


    QuartzDO getJobById(String id);
}
