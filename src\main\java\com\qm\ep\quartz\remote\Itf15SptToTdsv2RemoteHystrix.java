package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToTdsv2RemoteHystrix extends QmRemoteHystrix<Itf15SptToTdsv2Remote> implements Itf15SptToTdsv2Remote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 SptToTdsv2：传输运费结算单数据
     */
    @Override
    public JsonResultVo<Object> transferDataOfSptb070CD(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDataOfSptb070CD".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 001 SptToTdsv2：插入采购结算池数据(异步)
     */
    @Override
    public JsonResultVo<Object> transferDataOfInsertSalb046(String companyId,
                                                            String custGroupId,
                                                            String tenantId,
                                                            String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDataOfInsertSalb046".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 - 001 SptToTdsv2：插入采购结算池数据(异步)
     */
    @Override
    public JsonResultVo<Object> transferDataOfInsertSalb046v6(String companyId,
                                                            String custGroupId,
                                                            String tenantId,
                                                            String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToV6:transferDataOfInsertSalb046v6".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 001 SptToTdsv2：删除采购结算池数据(异步)
     */
    @Override
    public JsonResultVo<Object> transferDataOfDeleteSalb046(String companyId,
                                                            String custGroupId,
                                                            String tenantId,
                                                            String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDataOfDeleteSalb046".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> transferDataOfDeleteSalb046v6(String companyId,
                                                            String custGroupId,
                                                            String tenantId,
                                                            String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDataOfDeleteSalb046v6".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    @Override
    public JsonResultVo<Object> transferData(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId){
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferData".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 - 提车出库  新能源 spt传给v6
     */
    @Override
    public JsonResultVo<Object> transferDatav6(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId){
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDatav6".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    /**
     * 定时任务 - 提车出库  新能源 spt传给fin 补传
     */
    @Override
    public JsonResultVo<Object> transferDataRepair(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId){
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToTdsv2:transferDataRepair".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
