package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ClassName:ActQuotaService
 * package:com.qm.ep.quartz.service
 * Description:
 *
 * @Date:2021/6/15 14:04
 * @Author:zyz
 */

public interface TdsServiceCpyService {
    JsonResultVo sendBankMessage(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage1(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage2(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage3(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage4(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage5(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage6(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage7(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage8(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage10(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage11(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage12(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage13(@RequestParam String  bankCode);
    JsonResultVo sendBankMessage14(@RequestParam String  bankCode);
    //运维单独发送消息
    JsonResultVo sendBankMessage80(@RequestParam String  bankCode,String tranfunc);
    JsonResultVo pushCd();
    JsonResultVo pushF1f2();
}

