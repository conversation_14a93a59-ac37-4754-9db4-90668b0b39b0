package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.TdsSpaRemote;
import com.qm.ep.quartz.service.TdsSpaService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TdsSpaServiceImpl implements TdsSpaService {

    @Autowired
    private TdsSpaRemote tdsSpaRemote;


    @Override
    public JsonResultVo calculationWarehouse() {
        return tdsSpaRemote.calculationWarehouse("15");
    }
}
