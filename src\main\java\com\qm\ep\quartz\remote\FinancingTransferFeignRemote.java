package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**l
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "tds-service-sal-invoice", fallbackFactory = FinancingTransferFeignFallbackFactory.class)
public interface FinancingTransferFeignRemote {

    /**
     * 融资转账
     */
    @PostMapping("/salb091C/financingTransfer")
    JsonResultVo financingTransfer(@RequestHeader("tenantId") String tenantId);

    /**
     * 返利匹配
     */
    @PostMapping("/salb091C/marry")
    JsonResultVo marry(@RequestHeader("tenantId") String tenantId);

    /**
     * 融资逆转账
     */
    @PostMapping("/salb091C/financingTransferF")
    JsonResultVo financingTransferF(@RequestHeader("tenantId") String tenantId);

    /**
    * @Description: 百望云上传数据
    * @Param: [s]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2021/11/26
    */
    @PostMapping("/salb091C/uploadBaiwangInvoice")
    JsonResultVo<Object> uploadBaiwangInvoice(@RequestHeader("tenantId") String tenantId);

    /**
    * @Description: 百望云发票预览
    * @Param: [s]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2021/11/26
    */
    @PostMapping("/salb091C/preBaiInvoice")
    JsonResultVo<Object> preBaiInvoice(@RequestHeader("tenantId") String tenantId);
}
