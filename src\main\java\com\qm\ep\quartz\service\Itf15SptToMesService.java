package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * @author: jianghong
 * @time: 2021/4/14 19:56
 */
public interface Itf15SptToMesService {
    /**
     * 定时任务 - 001 SptToMes：传输库存数据
     */
    JsonResultVo<Object> transferDataOfInventory(String loginKey);

    /**
     * 定时任务 - 002 SptToMes：蔚山1厂下线入库回传接口(恒展系统)
     */
    JsonResultVo<Object> transferToHzmes(String loginKey);

    /**
     * 定时任务 - 003 SptToMes：繁荣工厂下线入库回传接口
     */
    JsonResultVo<Object> transferToFrmom(String loginKey);
    /**
     * 定时任务 - 004 SptToMes：蔚山2工厂下线入库回传接口
     */
    JsonResultVo<Object> transferToWs2mom(String loginKey);
}
