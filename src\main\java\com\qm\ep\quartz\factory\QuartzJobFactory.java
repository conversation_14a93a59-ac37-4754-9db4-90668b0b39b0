package com.qm.ep.quartz.factory;

import com.qm.ep.quartz.task.WelcomeJob;
import com.qm.ep.quartz.task.extend.AsynchronousWelJob;
import com.qm.ep.quartz.task.extend.SynchronousWelJob;
import org.quartz.Job;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/13
 */
public enum QuartzJobFactory {
    /**
     * 同步job
     */
    SYNC_HRONOUS_WEL_JOB(SynchronousWelJob.class),
    //异步Job
    ASYNC_HRONOUS_WEL_JOB(AsynchronousWelJob.class);

    private Class<? extends WelcomeJob> aClass;

    QuartzJobFactory(Class<? extends WelcomeJob> aClass) {
        this.aClass = aClass;
    }


    /**
     * 返回同步执行Class
     *
     * @param isSyncJob
     * @return
     */
    public static Class<? extends Job> getJob(String isSyncJob) {
        if (!"1".equals(isSyncJob)) {
            return SYNC_HRONOUS_WEL_JOB.aClass;
        }
        return ASYNC_HRONOUS_WEL_JOB.aClass;
    }
}
