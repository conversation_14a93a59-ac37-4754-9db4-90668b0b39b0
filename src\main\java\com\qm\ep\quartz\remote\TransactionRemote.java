package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@Repository
@FeignClient(name = "tds-service-sal-plan", fallbackFactory = TransationFallbackFactory.class)
public interface TransactionRemote {

    /**
     * 交易自动作废
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/4/27
     */
    @PostMapping("/salb183/transationVoid")
    JsonResultVo<Object> transationVoid(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 周订单周末释放
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/4/27
     */
    @PostMapping("/salb001/weekendRelease")
    JsonResultVo<Object> weekRelease(@RequestParam("day") String day, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 月计划高排接
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/21
     */
    @PostMapping("/salb146c/erpSaljhpcjg")
    JsonResultVo<Object> erpSaljhpcjg(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 月计划订单计划接口
     *
     * @Param: []
     * @return:
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/salb146c/erpSalDdjh")
    JsonResultVo<Object> erpSalDdjh(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 订单中心——订单入库信息
     *
     * @Param: [addRess, tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/interfaceerp/sendDDRKToERP")
    JsonResultVo<Object> sendDDRKToERP(@RequestParam("addRess") String addRess, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 发运出库信息
     *
     * @Param: [addRess, tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/interfaceerp/sendFYCKToERP")
    JsonResultVo<Object> sendFYCKToERP(@RequestParam("addRess") String addRess, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 经销商入库信息
     *
     * @Param: [addRess, tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/interfaceerp/sendJXSRKToERP")
    JsonResultVo<Object> sendJXSRKToERP(@RequestParam("addRess") String addRess, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 客户交付信息
     *
     * @Param: [addRess, tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/interfaceerp/sendKHJFToERP")
    JsonResultVo<Object> sendKHJFToERP(@RequestParam("addRess") String addRess, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 人工日历信息
     *
     * @Param: [addRess, tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/interfaceerp/sendRGRLToERP")
    JsonResultVo<Object> sendRGRLToERP(@RequestParam("addRess") String addRess, @RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 同步电商中心FBOM接口
     *
     * @Param: []
     * @return:
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @PostMapping("/ifFbomDsOut003/syncVehicleFeature")
    JsonResultVo<Object> syncVehicleFeature(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 同步DMS订单
     *
     * @Param: []
     * @return:
     * @Author: 许可
     * @Date: 2022/5/10
     */
    @PostMapping("/dmscrmb003/syncDmsOrder")
    JsonResultVo<Object> syncDmsOrder(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * @Description: 同步进出口计划日历
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/5/19
     */
    @PostMapping("/planningCalendar/sendDDRKToJCK")
    JsonResultVo<Object> sendPlanCalerderToJCK(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 给OTD同步经销商
     *
     * @Param: []
     * @return:
     * @Author: 许可
     * @Date: 2022/5/10
     */
    @PostMapping("/interfaceerp/sendDealerInfoToOTD")
    JsonResultVo<Boolean> sendDealerInfoToOTD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * @Description: 定时任务-匹配排产明细数据
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    @PostMapping("/salb001d/sendMatchPorduct")
    JsonResultVo<Object> sendMatchPorduct(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * @Description: 定时任务-AB类订单明细释放
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    @PostMapping("/salb001d/weekEditRelease")
    JsonResultVo<Object> weekEditRelease(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 同步DMS销售订单上传记录
     *
     * @Param: []
     * @return:
     * @Author: 许可
     * @Date: 2022/12/10
     */
    @PostMapping("/dmsOrdersUploadLog/syncDmsOrdersUploadLog")
    JsonResultVo<Object> syncDmsOrdersUploadLog(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
    * @Description: 下发客户订单进度变动信息
    * @Param: [s, s1]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/4/14
    */
    @PostMapping("/salb144/custOrderProcessChang")
    JsonResultVo<Object> custOrderProcessChang(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

}