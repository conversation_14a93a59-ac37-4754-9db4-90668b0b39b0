package com.qm.ep.quartz.domain.dto;

import com.qm.ep.quartz.domain.bean.Mdac315CDO;
import com.qm.ep.quartz.domain.bean.Mdac315DDO;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/10/16
 **/
@Schema(title = "大客户资源申请申请主子表传入对象", description = "大客户资源申请申请主子表传入对象")
@Data
public class Mdac315MiddleDTO {
    private static final long serialVersionUID = 1L;
    @Schema(title = "主表信息", description = "主表信息")
    private Mdac315CDO main;

    @Schema(title = "子表信息", description = "子表信息")
    private List<Mdac315DDO> childList;
}