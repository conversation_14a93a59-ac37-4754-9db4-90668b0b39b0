package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.service.Itf15VlmsToSptService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: jianghong
 * @time: 2021/4/2 14:17
 */
@Tag(name = "调试定时任务使用", description = "调试定时任务使用")
@RestController
@RequestMapping("/itf15VlmsToSpt")
public class Itf15VlmsToSptController extends BaseController {
    @Autowired
    Itf15VlmsToSptService itf15VlmsToSptService;

    /**
     * 定时任务 - VlmsToSpt：司机信息 001
     */
    @Operation(summary = "司机信息 001", description = "VlmsToSpt：司机信息 001[author:10027705]")
    @PostMapping("/transferDataOfsjxx")
    public JsonResultVo transferDataOfsjxx(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfsjxx(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：运输车信息 002
     */
    @Operation(summary = "运输车信息 002", description = "VlmsToSpt：运输车信息 002[author:10027705]")
    @PostMapping("/transferDataOfyscxx")
    public JsonResultVo transferDataOfyscxx(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfyscxx(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003
     */
    @Operation(summary = "指派运输商 003", description = "VlmsToSpt：指派运输商 003[author:10027705]")
    @PostMapping("/transferDataOfzpyss")
    public JsonResultVo transferDataOfzpyss(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfzpyss(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：取消指派运输商 004
     */
    @Operation(summary = "取消指派运输商 004", description = "VlmsToSpt：取消指派运输商 004[author:10027705]")
    @PostMapping("/transferDataOfqxzpyss")
    public JsonResultVo transferDataOfqxzpyss(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfqxzpyss(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：变更运输商 005
     */
    @Operation(summary = "变更运输商 005", description = "VlmsToSpt：变更运输商 005[author:10027705]")
    @PostMapping("/transferDataOfbgyss")
    public JsonResultVo transferDataOfbgyss(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfbgyss(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：打印提车单 006
     */
    @Operation(summary = "打印提车单 006", description = "VlmsToSpt：打印提车单 006[author:10027705]")
    @PostMapping("/transferDataOfdytcd")
    public JsonResultVo transferDataOfdytcd(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfdytcd(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：车辆位置信息 007
     */
    @Operation(summary = "车辆位置信息 007", description = "VlmsToSpt：车辆位置信息 007[author:10027705]")
    @PostMapping("/transferDataOfgpszt")
    public JsonResultVo transferDataOfgpszt(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfgpszt(loginKey);
    }

    /**
     * 定时任务 - VlmsToSpt：在途时间点 008
     */
    @Operation(summary = "在途时间点 008", description = "VlmsToSpt：在途时间点 008[author:10027705]")
    @PostMapping("/transferDataOfsjztsj")
    public JsonResultVo transferDataOfsjztsj(@RequestBody String loginKey) {
        return itf15VlmsToSptService.transferDataOfsjztsj(loginKey);
    }
}
