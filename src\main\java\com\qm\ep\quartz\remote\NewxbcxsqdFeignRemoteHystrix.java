package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class NewxbcxsqdFeignRemoteHystrix extends QmRemoteHystrix<NewxbcxsqdRemote> implements NewxbcxsqdRemote {

    @Override
    public JsonResultVo<Object> btsalb717(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> btsalb720(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> btsalb731(String tenantId) {
        return getResult();
    }


}
