package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToMesRemote;
import com.qm.ep.quartz.service.Itf15SptToMesService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jianghong
 * @time: 2021/4/14 19:57
 */
@Service
public class Itf15SptToMesServiceImpl implements Itf15SptToMesService {
    @Autowired
    Itf15SptToMesRemote itf15SptToMesRemote;

    /**
     * 定时任务 - 001 SptToMes：传输库存数据
     */
    @Override
    public JsonResultVo<Object> transferDataOfInventory(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToMesRemote.transferDataOfInventory(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 002 SptToMes：蔚山1厂下线入库回传接口(恒展系统)
     */
    @Override
    public JsonResultVo<Object> transferToHzmes(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToMesRemote.transferToHzmes(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 003 SptToMes：繁荣工厂下线入库回传接口
     */
    @Override
    public JsonResultVo<Object> transferToFrmom(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToMesRemote.transferToFrmom(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 004 SptToMes：蔚山2工厂下线入库回传接口
     */
    @Override
    public JsonResultVo<Object> transferToWs2mom(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToMesRemote.transferToWs2mom(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
