package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToTdsv2FallbackFactory.class)
public interface Itf15SptToTdsv2Remote {

    /**
     * 定时任务 - 001 SptToTdsv2：传输运费结算单数据
     */
    @PostMapping("/sptToTdsv2/transferDataOfSptb070CD")
    JsonResultVo<Object> transferDataOfSptb070CD(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 002 SptToTdsv2：插入采购结算池数据(异步)
     */
    @PostMapping("/sptToTdsv2/transferDataOfInsertSalb046")
    JsonResultVo<Object> transferDataOfInsertSalb046(@RequestHeader("companyId") String companyId,
                                                     @RequestHeader("custGroupId") String custGroupId,
                                                     @RequestHeader("tenantId") String tenantId,
                                                     @RequestHeader("userId") String userId);

    /**
     * 定时任务v6 - 002 SptToTdsv2：插入采购结算池数据(异步)
     */
    @PostMapping("/sptToTdsv2/transferDataOfInsertSalb046v6")
    JsonResultVo<Object> transferDataOfInsertSalb046v6(@RequestHeader("companyId") String companyId,
                                                     @RequestHeader("custGroupId") String custGroupId,
                                                     @RequestHeader("tenantId") String tenantId,
                                                     @RequestHeader("userId") String userId);


    /**
     * 定时任务 - 003 SptToTdsv2：删除采购结算池数据(异步)
     */
    @PostMapping("/sptToTdsv2/transferDataOfDeleteSalb046")
    JsonResultVo<Object> transferDataOfDeleteSalb046(@RequestHeader("companyId") String companyId,
                                                     @RequestHeader("custGroupId") String custGroupId,
                                                     @RequestHeader("tenantId") String tenantId,
                                                     @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 003 SptToTdsv2：删除采购结算池数据(异步)
     */
    @PostMapping("/sptToTdsv2/transferDataOfDeleteSalb046v6")
    JsonResultVo<Object> transferDataOfDeleteSalb046v6(@RequestHeader("companyId") String companyId,
                                                     @RequestHeader("custGroupId") String custGroupId,
                                                     @RequestHeader("tenantId") String tenantId,
                                                     @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    @PostMapping("/itfepsalb030/transferData")
    JsonResultVo<Object> transferData(@RequestHeader("companyId") String companyId,
                                      @RequestHeader("custGroupId") String custGroupId,
                                      @RequestHeader("tenantId") String tenantId,
                                      @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    @PostMapping("/itfepsalb030/transferDatav6")
    JsonResultVo<Object> transferDatav6(@RequestHeader("companyId") String companyId,
                                      @RequestHeader("custGroupId") String custGroupId,
                                      @RequestHeader("tenantId") String tenantId,
                                      @RequestHeader("userId") String userId);



    /**
     * 定时任务 - 提车出库  新能源 spt传给fin 补传
     */
    @PostMapping("/itfepsalb030h/transferDataRepair")
    JsonResultVo<Object> transferDataRepair(@RequestHeader("companyId") String companyId,
                                      @RequestHeader("custGroupId") String custGroupId,
                                      @RequestHeader("tenantId") String tenantId,
                                      @RequestHeader("userId") String userId);

}
