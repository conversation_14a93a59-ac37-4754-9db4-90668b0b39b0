package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendDealerToDCEPRemote;
import com.qm.ep.quartz.service.DailySettleService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DailySettleImpl implements DailySettleService {

    @Autowired
    private SendDealerToDCEPRemote SendDealerToDCEPRemote;

    @Override
    public JsonResultVo daliySettle() {
        return SendDealerToDCEPRemote.salb021DailySettle("15","6000");
    }


}
