package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class SpaSaleFeignFallbackFactory implements FallbackFactory<SpaSaleFeignRemote> {

    @Override
    public SpaSaleFeignRemote create(Throwable throwable) {
        SpaSaleFeignRemoteHystrix feignRemoteHystrix = new SpaSaleFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
