package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.Salb235Remote;
import com.qm.ep.quartz.service.Salb235Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 维护试驾车申请扣费失败重新扣费服务
 *
 * <AUTHOR>
 * @since 2024/2/26
 */
@Service
public class Salb235ServiceImpl implements Salb235Service {

    @Autowired
    private Salb235Remote salb235Remote;

    /**
     * 扣费失败重新扣费
     */
    @Override
    public JsonResultVo<Object> reTryDeductionFailedJob() {
        return salb235Remote.reTryDeductionFailedJob("15");
    }
}
