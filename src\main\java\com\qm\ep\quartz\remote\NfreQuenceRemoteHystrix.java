package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 批量更新索赔频次
 */
@Component
@Slf4j
@Data
public class NfreQuenceRemoteHystrix extends QmRemoteHystrix<NfreQuenceRemote> implements NfreQuenceRemote {

    @Override
    public JsonResultVo<Object> nfreQuence(String tenantId) {
        return getResult();
    }
}
