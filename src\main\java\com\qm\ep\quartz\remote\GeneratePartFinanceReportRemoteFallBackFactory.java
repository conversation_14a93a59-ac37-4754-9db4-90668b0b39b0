package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class GeneratePartFinanceReportRemoteFallBackFactory implements FallbackFactory<GeneratePartFinanceReportRemote> {
    @Override
    public GeneratePartFinanceReportRemote create(Throwable throwable) {
        GeneratePartFinanceReportRemoteHystrix generatePartFinanceReportRemoteHystrix = new GeneratePartFinanceReportRemoteHystrix();
        generatePartFinanceReportRemoteHystrix.setHystrixEx(throwable);
        return generatePartFinanceReportRemoteHystrix;
    }
}
