package com.qm.ep.quartz.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 维护大客户资源申请单主表（V2：SALB402C）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mdac315c")
@Schema(title = "维护大客户资源申请单主表（V2：SALB402C）", description = "维护大客户资源申请单主表（V2：SALB402C）")
public class Mdac315CDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "id", description = "id")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(title = "申请单号", description = "申请单号")
    @TableField("VBILLNO")
    private String vbillno;


    @Schema(title = "经销商id", description = "经销商id")
    @TableField("NDEALERID")
    private String ndealerid;

    @Schema(title = "客户ID  mdac301c.id", description = "客户ID  mdac301c.id")
    @TableField("NCUSTOMERID")
    private String ncustomerid;

    @Schema(title = "联系人", description = "联系人")
    @TableField("VLINKMAN")
    private String vlinkman;

    @Schema(title = "联系方式", description = "联系方式")
    @TableField("VTEL")
    private String vtel;

    @Schema(title = "备注", description = "备注")
    @TableField("VREMARK")
    private String vremark;

    @Schema(title = "特殊配置需求", description = "特殊配置需求")
    @TableField("VPRODUCTDESC")
    private String vproductdesc;

    @Schema(title = "经销商批发负责人", description = "经销商批发负责人")
    @TableField("VMANAGER")
    private String vmanager;

    @Schema(title = "联系电话", description = "联系电话")
    @TableField("VTEL1")
    private String vtel1;

    @Schema(title = "销售域", description = "销售域")
    @TableField("VSALEAREA")
    private String vsalearea;

    @Schema(title = "销售域名称", description = "销售域名称")
    @TableField(value = "VSALEAREATEXT", exist = false)
    private String vsaleareatext;

    @Schema(title = "审核意见", description = "审核意见")
    @TableField("VAUDITREMARK")
    private String vauditremark;

    @Schema(title = "操作员ID", description = "操作员ID")
    @TableField("NOPERATOR")
    private String noperator;

    @Schema(title = "操作日期", description = "操作日期")
    @TableField("DOPERATER")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date doperater;

    @Schema(title = "公司ID", description = "公司ID")
    @TableField(value = "NCOMPANYID")
    private String ncompanyid;

    @Schema(title = "状态  数据字典 SPCRESSTATUS", description = "状态  数据字典 SPCRESSTATUS")
    @TableField("VFINISHSTATE")
    private String vfinishstate;


    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(title = "提交日期", description = "提交日期")
    @TableField("DCOMMIT")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dcommit;

    @Schema(title = "一审日期", description = "一审日期")
    @TableField("DAUDIT")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date daudit;

    @Schema(title = "资源分配日期", description = "资源分配日期")
    @TableField("DASSIGN")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dassign;

    @Schema(title = "中止分配日期", description = "中止分配日期")
    @TableField("DBREAKASSIGN")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dbreakassign;

    @Schema(title = "大客户类别  数据字典 SPCFLAG", description = "大客户类别  数据字典 SPCFLAG")
    @TableField(value = "VSPCFLAG", exist = false)
    private String vspcflag;

    @Schema(title = "大客户一级分类  数据字典 SPCTYPE", description = "大客户一级分类  数据字典 SPCTYPE")
    @TableField(value = "VSPCTYPE", exist = false)
    private String vspctype;

    @Schema(title = "大客户二级分类  数据字典 SPCTYPE2", description = "大客户二级分类  数据字典 SPCTYPE2")
    @TableField(value = "VSPCTYPE2", exist = false)
    private String vspctype2;

    @Schema(title = "所属行业  数据字典 VOCATION", description = "所属行业  数据字典 VOCATION")
    @TableField(value = "VVOCATION", exist = false)
    private String vvocation;

    @Schema(title = "经销商代码", description = "经销商代码")
    @TableField(value = "VDEALER", exist = false)
    private String vdealer;

    @Schema(title = "经销商名称", description = "经销商名称")
    @TableField(value = "VDEALERNAME", exist = false)
    private String vdealername;

    @Schema(title = "组织id", description = "组织id")
    @TableField(value = "NORGAN", exist = false)
    private String norgan;

    @Schema(title = "经销商代码+名称", description = "经销商代码+名称")
    @TableField(value = "VDEALERANDNAME", exist = false)
    private String vdealerandname;

    @Schema(title = "客户地址", description = "客户地址")
    @TableField(value = "VCUSTOMERADDRESS", exist = false)
    private String vcustomeraddress;

    @Schema(title = "联系人姓名", description = "联系人姓名")
    @TableField(value = "vlinkname", exist = false)
    private String vlinkname;

    @Schema(title = "客户代码", description = "客户代码")
    @TableField(value = "VCUSTOMERCODE", exist = false)
    private String vcustomercode;

    @Schema(title = "客户名称", description = "客户名称")
    @TableField(value = "VNAME", exist = false)
    private String vname;
    @Schema(title = "客户地址", description = "客户地址")
    @TableField(value = "vaddr", exist = false)
    private String vaddr;
    @Schema(title = "证件类型  数据字典 VTMCUSTYPE", description = "证件类型  数据字典 VTMCUSTYPE")
    @TableField(value = "VCERTTYPE", exist = false)
    private String vcerttype;

    @Schema(title = "vmessage", description = "vmessage")
    @TableField(value = "vmessage", exist = false)
    private String vmessage;

    @Schema(title = "大区", description = "大区")
    @TableField(value = "vinsttext2", exist = false)
    private String vinsttext2;

    @Schema(title = "区域", description = "区域")
    @TableField(value = "vinsttext3", exist = false)
    private String vinsttext3;

    @Schema(title = "省区", description = "省区")
    @TableField(value = "vinsttext4", exist = false)
    private String vinsttext4;
}
