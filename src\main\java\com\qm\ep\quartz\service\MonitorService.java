package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * 服务监控中心 定时任务Task
 *
 * <AUTHOR>
 */
public interface MonitorService {

    /**
     * 每月1日，自动计算所有经销商的上月平均分
     *
     * @return
     */
    JsonResultVo<Boolean> distributorAverageLastOneMonth();

    /**
     * 每月1日，自动计算所有经销商的前12月平均分
     *
     * @return
     */
    JsonResultVo<Boolean> distributorAverageLastTwelveMonth();

    /**
     * "未机审索赔单预审（机审后调规则库部分）
     *
     * @return
     */
    JsonResultVo<Boolean> doBRScene4NoSampledClmBill();
}
