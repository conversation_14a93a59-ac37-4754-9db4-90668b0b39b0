package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendSaleForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendSaleForTspImpl implements SendSaleForTspService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo<String> getnewSaleReportForTsp() {
        return customRemote.getnewSaleReportForTsp("15", "6000");
    }

    @Override
    public JsonResultVo<String> getnewfailedSaleReportForTsp() {
        return customRemote.getnewfailedSaleReportForTsp("15", "6000");
    }


}
