package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class WorkFlowLogRemoteHystrix extends QmRemoteHystrix<WorkFlowLogRemote> implements WorkFlowLogRemote{

    @Override
    public JsonResultVo<Object> del() {
        return getResult();
    }


}
