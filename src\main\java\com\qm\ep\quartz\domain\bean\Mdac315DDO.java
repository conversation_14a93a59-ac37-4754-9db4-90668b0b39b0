package com.qm.ep.quartz.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * 维护大客户资源申请单明细表（V2：SALB402D）
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("mdac315d")
@Schema(title = "维护大客户资源申请单明细表（V2：SALB402D）", description = "维护大客户资源申请单明细表（V2：SALB402D）")
public class Mdac315DDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "ID", description = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @Schema(title = "主表id", description = "主表id")
    @TableField("NMAINID")
    private String nmainid;

    @Schema(title = "产品id", description = "产品id")
    @TableField("NPRODUCTID")
    private String nproductid;

    @Schema(title = "需求数量", description = "需求数量")
    @TableField("NQTY")
    private Integer nqty;

    @Schema(title = "需求数量", description = "需求数量")
    @TableField(value = "NQTY1", exist = false)
    private Integer nqty1;

    @Schema(title = "分配数量", description = "分配数量")
    @TableField("NAVALRESOURCE")
    private Integer navalresource;

    @Schema(title = "需求日期", description = "需求日期")
    @TableField("DPLANDATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date dplandate;

    @Schema(title = "分配年月", description = "分配年月")
    @TableField("VPLANYM")
    private String vplanym;

    @Schema(title = "分配周（期段）", description = "分配周（期段）")
    @TableField("NPERIOD")
    private Integer nperiod;

    @Schema(title = "提车价", description = "提车价")
    @TableField("NBASICPRICE")
    private Double nbasicprice;

    @Schema(title = "选装单价", description = "选装单价")
    @TableField("NPICKPRICE")
    private Double npickprice;

    @Schema(title = "金额", description = "金额")
    @TableField("NAMT")
    private Double namt;

    @Schema(title = "批售奖励-正常批售返利支持(元)", description = "批售奖励-正常批售返利支持(元)")
    @TableField("NSALREWARD1")
    private Double nsalreward1;

    @Schema(title = "批售奖励-特殊批售支持(元)", description = "批售奖励-特殊批售支持(元)")
    @TableField("NSALREWARD2")
    private Double nsalreward2;

    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(title = "选装名称", description = "选装名称")
    @TableField(value = "VOPTIONSTEXT", exist = false)
    private String voptionstext;

    @Schema(title = "产品名称", description = "产品名称")
    @TableField(value = "vproductname", exist = false)
    private String vproductname;

    @Schema(title = "销售车型", description = "销售车型")
    @TableField(value = "VSALTYPE", exist = false)
    private String vsaltype;

    @Schema(title = "车身颜色", description = "车身颜色")
    @TableField(value = "VCOLORTEXT", exist = false)
    private String vcolortext;

    @Schema(title = "内饰颜色", description = "内饰颜色")
    @TableField(value = "VCABTYPETEXT", exist = false)
    private String vcabtypetext;

    @Schema(title = "系列", description = "系列")
    @TableField(value = "VPDTSERIESTEXT", exist = false)
    private String vpdtseriestext;

    @Schema(title = "产品代码", description = "产品代码")
    @TableField(value = "VPRODUCTCODE", exist = false)
    private String vproductcode;

    @Schema(title = "产品分类", description = "产品分类")
    @TableField(value = "VREADYSALETYPETEXT", exist = false)
    private String vreadysaletypetext;

    @Schema(title = "联系人", description = "联系人")
    @TableField(value = "VLINKMAN", exist = false)
    private String vlinkman;

    @Schema(title = "联系电话", description = "联系电话")
    @TableField(value = "VTEL", exist = false)
    private String vtel;

    @Schema(title = "销售域", description = "销售域")
    @TableField(value = "VSALEAREA", exist = false)
    private String vsalearea;

    @Schema(title = "销售域名称", description = "销售域名称")
    @TableField(value = "VSALEAREATEXT", exist = false)
    private String vsaleareatext;

    @Schema(title = "备注", description = "备注")
    @TableField(value = "VREMARK", exist = false)
    private String vremark;

    @Schema(title = "特殊配置需求", description = "特殊配置需求")
    @TableField(value = "VPRODUCTDESC", exist = false)
    private String vproductdesc;

    @Schema(title = "经销商批发负责人", description = "经销商批发负责人")
    @TableField(value = "VMANAGER", exist = false)
    private String vmanager;

    @Schema(title = "联系电话", description = "联系电话")
    @TableField(value = "VTEL1", exist = false)
    private String vtel1;

    @Schema(title = "审核意见", description = "审核意见")
    @TableField(value = "VAUDITREMARK", exist = false)
    private String vauditremark;

    @Schema(title = "完成状态", description = "完成状态")
    @TableField(value = "VFINISHSTATE", exist = false)
    private String vfinishstate;

    @Schema(title = "提交日期", description = "提交日期")
    @TableField(value = "DCOMMIT", exist = false)
    private String dcommit;

    @Schema(title = "审核日期", description = "审核日期")
    @TableField(value = "DAUDIT", exist = false)
    private String daudit;

    @Schema(title = "申请单号", description = "申请单号")
    @TableField(value = "VBILLNO", exist = false)
    private String vbillno;

    @Schema(title = "客户代码", description = "客户代码")
    @TableField(value = "VCUSTOMERCODE", exist = false)
    private String vcustomercode;

    @Schema(title = "客户名称", description = "客户名称")
    @TableField(value = "VNAME", exist = false)
    private String vname;

    @Schema(title = "客户地址", description = "客户地址")
    @TableField(value = "vaddr", exist = false)
    private String vaddr;
    @Schema(title = "经销商代码", description = "经销商代码")
    @TableField(value = "VDEALER", exist = false)
    private String vdealer;
    @Schema(title = "经销商名称", description = "经销商名称")
    @TableField(value = "VDEALERNAME", exist = false)
    private String vdealername;
    @Schema(title = "经销商ID", description = "经销商ID")
    @TableField(value = "NDEALERID", exist = false)
    private String ndealerid;
    @Schema(title = "可用资源", description = "可用资源")
    @TableField(value = "NCANUSE", exist = false)
    private Integer ncanuse;

    @Schema(title = "大区", description = "大区")
    @TableField(value = "vinsttext2", exist = false)
    private String vinsttext2;

    @Schema(title = "区域", description = "区域")
    @TableField(value = "vinsttext3", exist = false)
    private String vinsttext3;

    @Schema(title = "省区", description = "省区")
    @TableField(value = "vinsttext4", exist = false)
    private String vinsttext4;
}