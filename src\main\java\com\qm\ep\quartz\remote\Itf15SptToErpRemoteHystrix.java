package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToErpRemoteHystrix extends QmRemoteHystrix<Itf15SptToErpRemote> implements Itf15SptToErpRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 SptToErp：传输下线入库数据
     */
    @Override
    public JsonResultVo<Object> transferDataOferp001(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:transferDataOferp001".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 002 SptToErp：传输下线入库数据
     */
    @Override
    public JsonResultVo<Object> transferDataOferp002(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:transferDataOferp002".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 003 SptToErp：把下线入库数据插到ep中间表
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableOfErp001(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:insertMiddleTableOfErp001".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 004 SptToErp：把出库数据插到ep中间表
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableOfErp002(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:insertMiddleTableOfErp002".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 005 SptToErp：把下线入库数据从ep中间表插入epr中间表
     */
    @Override
    public JsonResultVo<Object> transferToErp001(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:transferToErp001".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 006 SptToErp：把出库数据从ep中间表插入epr中间表
     */
    @Override
    public JsonResultVo<Object> transferToErp002(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:transferToErp002".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
