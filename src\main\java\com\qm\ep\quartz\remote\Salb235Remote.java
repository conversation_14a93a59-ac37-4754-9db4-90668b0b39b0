package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = Salb235Hystrix.class)
public interface Salb235Remote {

    @PostMapping("/salb235/reTryDeductionFailedJob")
    JsonResultVo<Object> reTryDeductionFailedJob(@RequestHeader("tenantId") String tenantId);

}
