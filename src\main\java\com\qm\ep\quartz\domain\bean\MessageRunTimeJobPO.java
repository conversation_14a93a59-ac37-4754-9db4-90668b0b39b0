package com.qm.ep.quartz.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 发件箱发送日志
 *
 * <AUTHOR>
 * @Date 2021-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dlm_runtime_job")
@Schema(title = "发件箱定时任务表", description = "发件箱定时任务表")
public class MessageRunTimeJobPO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * dlm_message_notice id
     */
    @TableField("nmainid")
    private String nMainID;
    /**
     * 是否已经执行
     */
    @TableField("isrun")
    private String isRun;
    /**
     * 发送时间
     */
    @TableField("dsend")
    private Date dSend;

    @TableField("createBy")
    private String createBy;

    @TableField("createByName")
    private String createByName;

    @TableField("createOn")
    private Date createOn;

    @TableField("updateBy")
    private String updateBy;

    @TableField("updateOn")
    private Long updateOn;

    @TableField("record_version")
    private Integer recordVersion;

    @Schema(title = "按公司设置，对应公司ID", description = "按公司设置，对应公司ID")
    @TableField("ncompanyid")
    private String nCompanyId;

    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "DTSTAMP", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    @Version
    private Timestamp dtstamp;

    @Schema(title = "各个发送信息返回接口", description = "各个发送信息返回接口")
    @TableField("ncode")
    private Integer nCode;

}