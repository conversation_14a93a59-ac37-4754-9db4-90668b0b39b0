package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 定时任务 - 给DMS传输车籍信息(经销商信息同步)
 */

@Component
@Slf4j
public class SendDealerToDCEPFallbackFactory implements FallbackFactory<SendDealerToDCEPRemote> {

    @Override
    public SendDealerToDCEPRemote create(Throwable throwable) {
        SendDealerToDCEPRemoteHystrix SendDealerToDCEPRemoteHystrix = new SendDealerToDCEPRemoteHystrix();
        SendDealerToDCEPRemoteHystrix.setHystrixEx(throwable);
        return SendDealerToDCEPRemoteHystrix;
    }
}
