package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.HqNewIntelligentOrderJobRemote;
import com.qm.ep.quartz.service.HqNewIntelligentOrderJobService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库备件销量：从仓库出库的移动单品种+数量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-10
 */
@Service
public class HqNewIntelligentOrderJobServiceImpl  implements HqNewIntelligentOrderJobService {

    @Autowired
    private HqNewIntelligentOrderJobRemote hqNewIntelligentOrderJobRemote;

    @Override
    public JsonResultVo calculateStockPartMad() {

        return hqNewIntelligentOrderJobRemote.calculateStockPartMad("15");

    }

    @Override
    public JsonResultVo calculateStockWidth() {

        return hqNewIntelligentOrderJobRemote.calculateStockWidth("15");

    }

    @Override
    public JsonResultVo calculateStockPartRequir() {
        return hqNewIntelligentOrderJobRemote.calculateStockPartRequir("15");
    }

    @Override
    public JsonResultVo calculateStockPartParams() {
        return hqNewIntelligentOrderJobRemote.calculateStockPartParams("15");
    }

    @Override
    public JsonResultVo calculateStockThreeYearsSaleCount() {
        return hqNewIntelligentOrderJobRemote.calculateStockThreeYearsSaleCount("15");
    }
}
