package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToMps 储运和公安部系统接口
 */

@Component
@Slf4j
public class Itf15SptToMpsFallbackFactory implements FallbackFactory<Itf15SptToMpsRemote> {

    @Override
    public Itf15SptToMpsRemote create(Throwable throwable) {
        Itf15SptToMpsRemoteHystrix priceremotehystrix = new Itf15SptToMpsRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
