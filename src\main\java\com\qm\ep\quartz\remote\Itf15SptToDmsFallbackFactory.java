package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */

@Component
@Slf4j
public class Itf15SptToDmsFallbackFactory implements FallbackFactory<Itf15SptToDmsRemote> {

    @Override
    public Itf15SptToDmsRemote create(Throwable throwable) {
        Itf15SptToDmsRemoteHystrix priceremotehystrix = new Itf15SptToDmsRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
