package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class FinancingTransferFeignFallbackFactory implements FallbackFactory<FinancingTransferFeignRemote> {

    @Override
    public FinancingTransferFeignRemote create(Throwable throwable) {
        FinancingTransferFeignRemoteHystrix feignRemoteHystrix = new FinancingTransferFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
