package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.sendDingMessageService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendDingMessageImpl implements sendDingMessageService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * 试驾车定时发送钉钉消息
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo sendDingMessage() {
        return financeTransferRemote.sendDingMessage("15", "6000");
    }
}
