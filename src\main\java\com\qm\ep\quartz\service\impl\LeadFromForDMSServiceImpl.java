package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendDealerToDCEPRemote;
import com.qm.ep.quartz.service.LeadFromForDMSService;
import com.qm.tds.api.domain.JsonParamDto;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LeadFromForDMSServiceImpl implements LeadFromForDMSService {

    @Autowired
    SendDealerToDCEPRemote sendDealerToDCEPRemote;

    @Override
    public JsonResultVo leadFromForDMS() {
        JsonParamDto jsonParamDto = new JsonParamDto();
        jsonParamDto.setCurrentPage(1);
        return sendDealerToDCEPRemote.leadFromForDMS("15","6000", jsonParamDto);
    }
}
