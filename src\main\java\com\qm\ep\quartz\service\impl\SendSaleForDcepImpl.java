package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendSaleForDcepService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendSaleForDcepImpl implements SendSaleForDcepService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo<String> getnewSaleReportForDCEP() {
        return customRemote.getnewSaleReportForDCEP("15", "6000");
    }


}
