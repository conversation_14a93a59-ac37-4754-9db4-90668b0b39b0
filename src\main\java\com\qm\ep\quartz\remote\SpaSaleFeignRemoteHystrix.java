package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SpaSaleFeignRemoteHystrix extends QmRemoteHystrix<SpaSaleFeignRemote> implements SpaSaleFeignRemote {
    @Override
    public JsonResultVo<Object> AutoFormSaleOrder(String tenantId) {
        return getResult();
    }

    /**
     * 定时任务维护收货通知单 申请作废 超过24小时短信发送
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo sendSMSByTwentyFourHours(String tenantId) {
        return getResult();
    }

    /**
     * 定时任务维护收货通知单 申请作废 超过４８小时短信发送
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo sendSMSByFortyEightHours(String tenantId) {
        return getResult();
    }

    /**
     * 新采购算法，定时加工 nbo  删除临时表 spawnbo1 和 spawnbo2
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo newAlgorithmCalculateNbo(String tenantId) {
        return getResult();
    }

}
