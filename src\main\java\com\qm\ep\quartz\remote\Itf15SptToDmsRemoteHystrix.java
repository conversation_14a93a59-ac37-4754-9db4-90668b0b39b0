package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToDmsRemoteHystrix extends QmRemoteHystrix<Itf15SptToDmsRemote> implements Itf15SptToDmsRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 SptToDms：传输采购入库数据
     */
    @Override
    public JsonResultVo<Object> purchaseConfirmEPAsyn(String companyId,
                                                      String custGroupId,
                                                      String tenantId,
                                                      String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToDms:purchaseConfirmEPAsyn".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 002 SptToDms：传输采购退库数据
     */
    @Override
    public JsonResultVo<Object> purchaseReturnEPAsyn(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToDms:purchaseReturnEPAsyn".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    /**
     * 定时任务 - 003 DmsToSpt：从dms获取到货确认数据并自动确认
     */
    @Override
    public JsonResultVo<Object> executeItf15_spt_dms_salb027_failure(String companyId,
                                                                     String custGroupId,
                                                                     String tenantId,
                                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToDms:executeItf15_spt_dms_salb027_failure".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
