package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-bank", fallbackFactory = SendBankFallback.class)
public interface SendBankRemote {
    //TDS传送平安银行数据
    @PostMapping("/pingAn/send")
    JsonResultVo sendBankPingAn(@RequestHeader("tenantId") String tenantId);


}