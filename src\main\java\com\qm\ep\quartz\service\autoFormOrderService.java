package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface autoFormOrderService {


    /**
     * 定时任务 -自动形成提车单-
     */
    JsonResultVo<Boolean> autoFormOrder(String loginKey);
    /**
     * 定时任务 -自动形成提车单- app
     */
    JsonResultVo<Boolean> autoFormOrderIwork(String loginKey);

    /**
     * 定时任务 -自动形成提车单-
     */
    JsonResultVo<Boolean> autoFormOrderForSalb008e(String loginKey);
    /**
     * 定时任务 -自动形成提车单-试驾车
     */
    JsonResultVo<Boolean> autoFormOrderForSalb23503(String loginKey);
    /**
     * 定时任务 -自动形成提车单-服务车
     */
    JsonResultVo<Boolean> autoFormOrderForSalb23507(String loginKey);
    /**
     * 定时任务 -发送钉钉消息-otd订单制 下线车辆提醒
     */
    JsonResultVo<Boolean> offlineDing(String loginKey);

    /**
     * 定时任务 -发送钉钉消息-otd订单制 48小时下线车辆提醒
     */
    JsonResultVo<Boolean> offlineBeforeTwoDays(String loginKey);

}
