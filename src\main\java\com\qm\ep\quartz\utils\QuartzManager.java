package com.qm.ep.quartz.utils;

import com.qm.ep.quartz.domain.other.ScheduleJob;
import com.qm.ep.quartz.factory.QuartzJobFactory;
import org.quartz.*;
import org.quartz.DateBuilder.IntervalUnit;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @title: QuartzManager.java
 * 计划任务管理
 */
@Service
public class QuartzManager {
    public final Logger log = LoggerFactory.getLogger(QuartzManager.class);
    /**
     * private SchedulerFactoryBean schedulerFactoryBean =SpringContextHolder.getBean(SchedulerFactoryBean.class);
     *
     * @Autowired
     * @Qualifier("schedulerFactoryBean") private SchedulerFactoryBean schedulerFactoryBean;
     */
    @Autowired
    private Scheduler scheduler;

    /**
     * 添加任务
     *
     * @param job 任务
     */
    public void addJob(ScheduleJob job) {
        try {

            // 创建jobDetail实例，绑定Job实现类
            // 指明job的名称，所在组的名称，以及绑定job类
            Class<? extends Job> jobClass = QuartzJobFactory.getJob(job.getIsParallel());

            String jobname = job.getJobName()+"_"+job.getId();
            log.info("---addJob----拼接完后的jobname-------"+jobname);

            JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(jobname, job.getJobGroup())// 任务名称和组构成任务key
                    .build();


            // 定义调度触发规则
            // 使用cornTrigger规则
            Trigger trigger = TriggerBuilder.newTrigger().withIdentity(jobname, job.getJobGroup())// 触发器key
                    .startAt(DateBuilder.futureDate(1, IntervalUnit.SECOND))
                    .withSchedule(CronScheduleBuilder.cronSchedule(job.getCronExpression())).startNow().build();
            // 把作业和触发器注册到任务调度中
            scheduler.scheduleJob(jobDetail, trigger);
            // 启动
            if (!scheduler.isShutdown()) {
                scheduler.start();
            }
        } catch (Exception e) {
            log.info("---error--添加定时任务失败！", e);
        }
    }

    /**
     * 获取所有计划中的任务列表
     *
     * @return 任务列表
     * @throws SchedulerException
     */
    public List<ScheduleJob> getAllJob() throws SchedulerException {
        GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
        Set<JobKey> jobKeys = scheduler.getJobKeys(matcher);
        List<ScheduleJob> jobList = new ArrayList<>();
        for (JobKey jobKey : jobKeys) {
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
            for (Trigger trigger : triggers) {
                ScheduleJob job = new ScheduleJob();
                job.setJobName(jobKey.getName());
                job.setJobGroup(jobKey.getGroup());
                job.setDescription("触发器:" + trigger.getKey());
                Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
                job.setJobStatus(triggerState.name());
                if (trigger instanceof CronTrigger) {
                    CronTrigger cronTrigger = (CronTrigger) trigger;
                    String cronExpression = cronTrigger.getCronExpression();
                    job.setCronExpression(cronExpression);
                }
                jobList.add(job);
            }
        }
        return jobList;
    }

    /**
     * 所有正在运行的job
     *
     * @return 任务列表
     * @throws SchedulerException
     */
    public List<ScheduleJob> getRunningJob() throws SchedulerException {
        List<JobExecutionContext> executingJobs = scheduler.getCurrentlyExecutingJobs();
        List<ScheduleJob> jobList = new ArrayList<>(executingJobs.size());
        for (JobExecutionContext executingJob : executingJobs) {
            ScheduleJob job = new ScheduleJob();
            JobDetail jobDetail = executingJob.getJobDetail();
            JobKey jobKey = jobDetail.getKey();
            Trigger trigger = executingJob.getTrigger();
            job.setJobName(jobKey.getName());
            job.setJobGroup(jobKey.getGroup());
            job.setDescription("触发器:" + trigger.getKey());
            Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
            job.setJobStatus(triggerState.name());
            if (trigger instanceof CronTrigger) {
                CronTrigger cronTrigger = (CronTrigger) trigger;
                String cronExpression = cronTrigger.getCronExpression();
                job.setCronExpression(cronExpression);
            }
            jobList.add(job);
        }
        return jobList;
    }

    /**
     * 判断Job是否存在
     *
     * @param scheduleJob 任务
     * @return true job存在；false job不存在。
     * @throws SchedulerException
     */
    public boolean existsJob(ScheduleJob scheduleJob) throws SchedulerException {

        String jobname = scheduleJob.getJobName()+"_"+scheduleJob.getId();
        log.info("-----existsJob--拼接完后的jobname-------"+jobname);

        JobKey jobKey = JobKey.jobKey(jobname, scheduleJob.getJobGroup());
        return scheduler.checkExists(jobKey);
    }

    /**
     * 暂停一个job
     *
     * @param scheduleJob 任务
     * @throws SchedulerException
     */
    public void pauseJob(ScheduleJob scheduleJob) throws SchedulerException {
        String jobname = scheduleJob.getJobName()+"_"+scheduleJob.getId();
        log.info("-----pauseJob--拼接完后的jobname-------"+jobname);

        JobKey jobKey = JobKey.jobKey(jobname, scheduleJob.getJobGroup());
        scheduler.pauseJob(jobKey);
    }

    /**
     * 恢复一个job
     *
     * @param scheduleJob 任务
     * @throws SchedulerException
     */
    public void resumeJob(ScheduleJob scheduleJob) throws SchedulerException {
        String jobname = scheduleJob.getJobName()+"_"+scheduleJob.getId();
        log.info("-----resumeJob--拼接完后的jobname-------"+jobname);

        JobKey jobKey = JobKey.jobKey(jobname, scheduleJob.getJobGroup());
        scheduler.resumeJob(jobKey);
    }

    /**
     * 删除一个job
     *
     * @param scheduleJob 任务
     * @throws SchedulerException
     */
    public void deleteJob(ScheduleJob scheduleJob) throws SchedulerException {
        String jobname = scheduleJob.getJobName()+"_"+scheduleJob.getId();
        log.info("-----deleteJob--拼接完后的jobname-------"+jobname);

        JobKey jobKey = JobKey.jobKey(jobname, scheduleJob.getJobGroup());
        TriggerKey triggerKey = TriggerKey.triggerKey(jobname, scheduleJob.getJobGroup());

        // 停止触发器
        scheduler.pauseTrigger(triggerKey);
        // 移除触发器
        scheduler.unscheduleJob(triggerKey);
        // 删除任务
        scheduler.deleteJob(jobKey);
    }

    /**
     * 立即执行job
     *
     * @param scheduleJob 任务
     * @throws SchedulerException
     */
    public void runAJobNow(ScheduleJob scheduleJob) throws SchedulerException {

        //JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
        if (!existsJob(scheduleJob)) {
            addJob(scheduleJob);
        }

        String jobname = scheduleJob.getJobName()+"_"+scheduleJob.getId();
        log.info("-----runAJobNow--拼接完后的jobname-------"+jobname);
        JobKey jobKey = JobKey.jobKey(jobname, scheduleJob.getJobGroup());

        scheduler.triggerJob(jobKey);
    }

    /**
     * 更新job时间表达式
     *
     * @param scheduleJob 任务
     * @throws SchedulerException
     */
    public void updateJobCron(ScheduleJob scheduleJob) throws SchedulerException {
//        TriggerKey triggerKey = TriggerKey.triggerKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
//        CronTrigger buildTrigger = TriggerBuilder.newTrigger().withIdentity(scheduleJob.getJobName(), scheduleJob.getJobGroup())// 触发器key
//                .startAt(DateBuilder.futureDate(1, IntervalUnit.SECOND))
//                .withSchedule(CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression())).startNow().build();
//        CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduleJob.getCronExpression());
//        buildTrigger = buildTrigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
//        scheduler.rescheduleJob(triggerKey, buildTrigger);

        deleteJob(scheduleJob);
        addJob(scheduleJob);
    }
}