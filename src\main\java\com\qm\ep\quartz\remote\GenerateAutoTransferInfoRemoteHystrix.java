package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @CreateTime 2020-09-30 8:33
 * @Version 1.0
 */
@Component
@Slf4j
@Data
public class GenerateAutoTransferInfoRemoteHystrix extends QmRemoteHystrix<GenerateAutoTransferInfoRemote> implements GenerateAutoTransferInfoRemote {

    @Override
    public JsonResultVo generateAutoTransferData(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo deleteTemp(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo autoDistributeByQuartz(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo syncSaleInvoiceToV6(String tenantId) {
        return getResult();
    }
}
