package com.qm.ep.quartz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.dto.MessageRunTimeJobDTO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.quartz.mapper.QuartzMapper;
import com.qm.ep.quartz.remote.FeignTempleRemote;
import com.qm.ep.quartz.service.QuartzService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.TableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
@Service
@Primary
public class QuartzServiceImpl extends QmBaseServiceImpl<QuartzMapper, QuartzDO> implements QuartzService {
    @Autowired
    private QuartzMapper quartzMapper;

    @Autowired
    private FeignTempleRemote feignTempleRemote;

    @Override
    public boolean saveOrUpdateWithHistory(QuartzDO queryMessage, LoginKeyDO loginKey) {
        return saveOrUpdate(queryMessage);
    }

    @Override
    public List<QuartzDO> selectDistinctGroup(Map map) {
        return baseMapper.selectDistinctGroup(map);
    }

    @Override
    public List<QuartzDO> selectDistinctName(Map map) {
        return baseMapper.selectDistinctName(map);
    }

    /**
     * 查询Job信息
     *
     * @param jobGroup  Job分组，即类名。
     * @param jobMethod Job名称，即函数名。
     * @return Job信息
     */
    @Override
    public QuartzDO getJob(String jobGroup, String jobMethod) {
        QueryWrapper<QuartzDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("jobGroup", jobGroup);
        queryWrapper.eq("jobName", jobMethod);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<QuartzStatisticsVO> executionStatistics(@RequestBody QuartzDTO dto) {
        return quartzMapper.statisticsTable(dto.getStartTime(), dto.getEndTime(), dto.getStatusList());
    }

    @Override
    public QmPage<QuartzStatisticsVO> executionStatisticsQuery(QuartzDTO dto) {
        IPage<QuartzStatisticsVO> querypage = TableUtils.convertToIPage(dto);
        IPage<QuartzStatisticsVO> page = quartzMapper.statisticsTableByQuery(querypage, dto.getStartTime(), dto.getEndTime(), dto.getStatusList());
        return TableUtils.convertQmPageFromMpPage(page);
    }

    @Override
    public JsonResultVo<List<MessageRunTimeJobPO>> getMessageRunTimeJobListForRun(MessageRunTimeJobDTO messageRunTimeJobDTO) {
        return feignTempleRemote.getMessageRunTimeJobListForRun(BootAppUtil.getLoginKey().getTenantId(), messageRunTimeJobDTO);
    }

    @Override
    public JsonResultVo<List<MessageRunTimeJobPO>> sendNoticeJob() {
        return feignTempleRemote.sendNoticeJob(BootAppUtil.getLoginKey().getTenantId(), BootAppUtil.getLoginKey().getCompanyId(), BootAppUtil.getLoginKey().getOperatorId());
    }



    /**
     * 查询Job信息 by id
     * @return Job信息
     */
    @Override
    public QuartzDO getJobById(String id) {
        QueryWrapper<QuartzDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return baseMapper.selectOne(queryWrapper);
    }

}
