package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcQAFeedbackBillFeignRemote;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.ep.quartz.service.SyncWarrantyPolicyService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SyncWarrantyPolicyServiceImpl implements SyncWarrantyPolicyService {

    @Autowired
    private SvcQAFeedbackBillFeignRemote feignRemote;

    @Autowired
    private QuartzLogService quartzLogService;

    @Override
    public JsonResultVo syncWarrantyPolicy() {
        log.info("SyncWarrantyPolicyService.syncWarrantyPolicy begin");
        String vLastDtstamp = quartzLogService.selectMaxJobDtstamp("com.qm.ep.quartz.service.SyncWarrantyPolicyService:syncWarrantyPolicy%");
        if (BootAppUtil.isNullOrEmpty(vLastDtstamp)) {
            vLastDtstamp = "1900-01-01";
        }
        log.debug("vLastDtstamp:" + vLastDtstamp);

        JsonResultVo vo =  feignRemote.syncWarrantyPolicy("15", "6000", vLastDtstamp);
        log.info("SyncWarrantyPolicyService.syncWarrantyPolicy end");
        return vo;
    }
}
