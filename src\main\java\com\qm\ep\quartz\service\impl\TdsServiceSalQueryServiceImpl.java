package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.domain.dto.InsertSalb063FiDTO;
import com.qm.ep.quartz.domain.dto.SearchPriceDTO;
import com.qm.ep.quartz.remote.TdsServiceSalQueryRemote;
import com.qm.ep.quartz.service.TdsServiceSalQueryService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service
@Slf4j
public class TdsServiceSalQueryServiceImpl implements TdsServiceSalQueryService {

    @Autowired
    private TdsServiceSalQueryRemote tdsServiceSalQueryRemote;
    @Override
    public JsonResultVo insertSalb063Fi() {
        return this.tdsServiceSalQueryRemote.insertSalb063Fi("15",new InsertSalb063FiDTO());
    }

    @Override
    public JsonResultVo insertPriceHistory() {

        log.info("同步财务价格开始：{}", LocalDateTime.now());

        //市场指导价
        SearchPriceDTO searchPriceDTO = new SearchPriceDTO();
        searchPriceDTO.setNpricestrategy("SCZDJ");
        searchPriceDTO.setStrategyId("212699307");
        searchPriceDTO.setDate(LocalDate.now());
        JsonResultVo history = this.tdsServiceSalQueryRemote.tdsHistoryV2("15", searchPriceDTO);
        log.info("市场指导价返回结果:{}", JSON.toJSONString(history.getData()));

        //采购价
        searchPriceDTO.setNpricestrategy("RK");
        searchPriceDTO.setStrategyId("209214812");
        searchPriceDTO.setDate(LocalDate.now());
        return this.tdsServiceSalQueryRemote.tdsHistoryV2("15", searchPriceDTO);
    }
}
