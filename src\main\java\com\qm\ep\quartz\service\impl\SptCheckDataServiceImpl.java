package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.SptCheckDataRemote;
import com.qm.ep.quartz.service.SptCheckDataService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jianghong
 * @time: 2021/6/30 10:40
 */
@Service
public class SptCheckDataServiceImpl implements SptCheckDataService {
    @Autowired
    SptCheckDataRemote sptCheckDataRemote;

    /**
     * 定时任务 - SptCheckData：001 处理salb021库存数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    @Override
    public JsonResultVo<Object> dealNinventoryOfSalb021(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return sptCheckDataRemote.dealNinventoryOfSalb021(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptCheckData：002 处理sptb023库存数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    @Override
    public JsonResultVo<Object> dealNinventoryOfSptb023(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return sptCheckDataRemote.dealNinventoryOfSptb023(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptCheckData：003 处理sptb023分配数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    @Override
    public JsonResultVo<Object> dealAssignOfSptb023(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return sptCheckDataRemote.dealAssignOfSptb023(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Boolean> updateSptc001(String loginKey) {
        return sptCheckDataRemote.updateSptc001("15","6000");
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("");
        }
        return loginKeyDO;
    }
}
