package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;

public class TdsServiceCpyHystrix extends QmRemoteHystrix<TdsServiceCpyRemote> implements TdsServiceCpyRemote{


    @Override
    public JsonResultVo sendBankMessage(String bankCode) {
        return getResult();
    }
    @Override
    public JsonResultVo sendBankMessage80(String bankCode, String tranfunc) {
        return getResult();
    }
    @Override
    public JsonResultVo pushCd() {
        return getResult();
    }

    @Override
    public JsonResultVo pushF1f2() {
        return getResult();
    }
}
