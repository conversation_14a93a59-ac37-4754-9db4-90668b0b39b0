package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FeignMonitorRemote;
import com.qm.ep.quartz.service.MonitorService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MonitorServiceImpl implements MonitorService {
    @Autowired
    private FeignMonitorRemote feignRemote;

    @Override
    public JsonResultVo<Boolean> distributorAverageLastOneMonth() {
        feignRemote.distributorAverageLastOneMonth("15");
        JsonResultVo<Boolean> res = new JsonResultVo<>();
        res.setData(true);
        return res;
    }

    @Override
    public JsonResultVo<Boolean> distributorAverageLastTwelveMonth() {
        feignRemote.distributorAverageLastTwelveMonth("15");
        return null;
    }

    @Override
    public JsonResultVo<Boolean> doBRScene4NoSampledClmBill() {
        feignRemote.doBRScene4NoSampledClmBill("15");
        JsonResultVo<Boolean> res = new JsonResultVo<>();
        res.setData(true);
        return res;
    }
}
