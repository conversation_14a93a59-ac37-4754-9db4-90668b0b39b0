package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
public interface SendSALB061ToV2Service {
    JsonResultVo sendDataToV2();
    /**
     *  EP-TDSV2 信用账户凭证接口
     * <AUTHOR>
     * @time: 2023/04/26
     */
    JsonResultVo transferSalb064ToTdsv2();

    //向DMS发送省市县
    JsonResultVo sendProvinceCityToDMS();

    //向DMS发送经销商信息
    JsonResultVo sendDealerInfoToDMS();

    //自动执行大客户资源申请资源分配 38365
    JsonResultVo mdac315cResourceAllocation();

    //向DMS发送产品价格
    JsonResultVo sendProductPriceToDMS();

    /**
     * 向新能源DMS发送产品价格
     *
     * @return
     */
    JsonResultVo sendProductPriceToDMSXny();
}