package com.qm.ep.quartz.utils;

import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.other.ScheduleJob;
import com.qm.ep.quartz.domain.vo.TaskVO;

public class ScheduleJobUtils {

    private ScheduleJobUtils() {
        // Do nothing because of X and Y.
    }

    public static ScheduleJob entityToData(QuartzDO scheduleJobEntity) {
        ScheduleJob scheduleJob = new ScheduleJob();
        scheduleJob.setBeanClass(scheduleJobEntity.getBeanClass());
        scheduleJob.setCronExpression(scheduleJobEntity.getCronExpression());
        scheduleJob.setDescription(scheduleJobEntity.getDescription());
        scheduleJob.setIsConcurrent(scheduleJobEntity.getIsConcurrent());
        scheduleJob.setIsParallel(scheduleJobEntity.getIsParallel());
        scheduleJob.setJobName(scheduleJobEntity.getJobName());
        scheduleJob.setJobGroup(scheduleJobEntity.getJobGroup());
        scheduleJob.setJobStatus(scheduleJobEntity.getJobStatus());
        scheduleJob.setMethodName(scheduleJobEntity.getMethodName());
        scheduleJob.setSpringBean(scheduleJobEntity.getSpringBean());
        scheduleJob.setId(scheduleJobEntity.getId());
        return scheduleJob;
    }

    public static ScheduleJob entityToVoData(TaskVO scheduleJobEntity) {
        ScheduleJob scheduleJob = new ScheduleJob();
        scheduleJob.setBeanClass(scheduleJobEntity.getBeanClass());
        scheduleJob.setCronExpression(scheduleJobEntity.getCronExpression());
        scheduleJob.setDescription(scheduleJobEntity.getDescription());
        scheduleJob.setIsConcurrent(scheduleJobEntity.getIsConcurrent());
        scheduleJob.setIsParallel(scheduleJobEntity.getIsParallel());
        scheduleJob.setJobName(scheduleJobEntity.getJobName());
        scheduleJob.setJobGroup(scheduleJobEntity.getJobGroup());
        scheduleJob.setJobStatus(scheduleJobEntity.getJobStatus());
        scheduleJob.setMethodName(scheduleJobEntity.getMethodName());
        scheduleJob.setSpringBean(scheduleJobEntity.getSpringBean());
        scheduleJob.setId(scheduleJobEntity.getId());
        return scheduleJob;
    }
}