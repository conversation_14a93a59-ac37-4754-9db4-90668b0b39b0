package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * l
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = itftosalFallbackFactory.class)
public interface itftosalRemote {


    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @PostMapping("/institution/dealsysc060list")
    JsonResultVo<Object> dealsysc060list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 -省市区县sysc005同步
     */
    @PostMapping("/city/dealsysc005list")
    JsonResultVo<Object> dealsysc005list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);


    /**
     * 定时任务 -人员授权公司sysi051同步
     */
    @PostMapping("/sysi051/dealsysi051list")
    JsonResultVo<Object> dealsysi051list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    @PostMapping("/sysc009d/dealsysc009dlist")
    JsonResultVo<Object> dealsysc009dlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac001c同步
     */
    @PostMapping("/mdac001c/dealmdac001clist")
    JsonResultVo<Object> dealmdac001clist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac001d同步
     */
    @PostMapping("/productItemD/dealmdac001dlist")
    JsonResultVo<Object> dealmdac001dlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac001p同步
     */
    @PostMapping("/mdac001p/dealmdac001plist")
    JsonResultVo<Object> dealmdac001plist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac001r同步
     */
    @PostMapping("/mdac001r/dealmdac001rlist")
    JsonResultVo<Object> dealmdac001rlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac002同步
     */
    @PostMapping("/mdac002/dealmdac002list")
    JsonResultVo<Object> dealmdac002list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac002d同步
     */
    @PostMapping("/mdac002d/dealmdac002dlist")
    JsonResultVo<Object> dealmdac002dlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac009同步
     */
    @PostMapping("/mdac009/dealmdac009list")
    JsonResultVo<Object> dealmdac009list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac009d同步
     */
    @PostMapping("/mdac009d/dealmdac009dlist")
    JsonResultVo<Object> dealmdac009dlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 -mdac600同步
     */
    @PostMapping("/mdac600/dealmdac600list")
    JsonResultVo<Object> dealmdac600list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);


    /**
     * 定时任务 -自动形成提车单
     */
    @PostMapping("/order/autoFormOrder")
    JsonResultVo<Boolean> autoFormOrder(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 -自动形成提车单工作台
     */
    @PostMapping("/order/autoFormOrderIwork")
    JsonResultVo<Boolean> autoFormOrderIwork(@RequestHeader("companyId") String companyId,
                                        @RequestHeader("custGroupId") String custGroupId,
                                        @RequestHeader("tenantId") String tenantId,
                                        @RequestHeader("userId") String userId);


    /**
     * 定时任务 -周订单指派 自动形成提车单
     */
    @PostMapping("/order/autoFormOrderForSalb008e")
    JsonResultVo<Boolean> autoFormOrderForSalb008e(@RequestHeader("companyId") String companyId,
                                        @RequestHeader("custGroupId") String custGroupId,
                                        @RequestHeader("tenantId") String tenantId,
                                        @RequestHeader("userId") String userId);

    /**
     * 定时任务 -试驾车 自动形成提车单
     */
    @PostMapping("/order/autoFormOrderForSalb23503")
    JsonResultVo<Boolean> autoFormOrderForSalb23503(@RequestHeader("companyId") String companyId,
                                                    @RequestHeader("custGroupId") String custGroupId,
                                                    @RequestHeader("tenantId") String tenantId,
                                                    @RequestHeader("userId") String userId);

    /**
     * 定时任务 -服务车 自动形成提车单
     */
    @PostMapping("/order/autoFormOrderForSalb23507")
    JsonResultVo<Boolean> autoFormOrderForSalb23507(@RequestHeader("companyId") String companyId,
                                                    @RequestHeader("custGroupId") String custGroupId,
                                                    @RequestHeader("tenantId") String tenantId,
                                                    @RequestHeader("userId") String userId);

    /**
     * 定时任务 -订单制 发送钉钉消息掉的钉钉接口 下线数据
     */
    @PostMapping("/order/offlineDing")
    JsonResultVo<Boolean> offlineDing(@RequestHeader("companyId") String companyId,
                                      @RequestHeader("custGroupId") String custGroupId,
                                      @RequestHeader("tenantId") String tenantId,
                                      @RequestHeader("userId") String userId);

    /**
     * 定时任务 -订单制两天前提前下发 发送钉钉消息掉的钉钉接口
     */
    @PostMapping("/order/offlineBeforeTwoDays")
    JsonResultVo<Boolean> offlineBeforeTwoDays(@RequestHeader("companyId") String companyId,
                                               @RequestHeader("custGroupId") String custGroupId,
                                               @RequestHeader("tenantId") String tenantId,
                                               @RequestHeader("userId") String userId);




    /**
     * 价格申请单-下发电商中心 市场指导价 2023.6.19
     */
    @PostMapping("/salb105dszx/sendProductPriceToDSZX")
    JsonResultVo<Boolean> sendProductPriceToDSZX(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
