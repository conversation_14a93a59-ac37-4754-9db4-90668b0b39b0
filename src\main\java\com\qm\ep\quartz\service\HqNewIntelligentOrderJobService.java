package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * <p>
 * 仓库备件销量：从仓库出库的移动单品种+数量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-10
 */
public interface HqNewIntelligentOrderJobService {

    JsonResultVo calculateStockPartMad();

    JsonResultVo calculateStockWidth();

    JsonResultVo calculateStockPartRequir();

    JsonResultVo calculateStockPartParams();

    JsonResultVo calculateStockThreeYearsSaleCount();


}
