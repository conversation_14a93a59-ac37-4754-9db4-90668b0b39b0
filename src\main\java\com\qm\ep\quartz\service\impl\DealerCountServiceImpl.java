package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.PriceRemote;
import com.qm.ep.quartz.service.DealerCountService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DealerCountServiceImpl implements DealerCountService {

    @Autowired
    private PriceRemote priceRemote;

    @Override
    public JsonResultVo dealerCountService() {
        return priceRemote.calculatefrequency("15");
    }

    @Override
    public JsonResultVo dealerFbCountService() {
        return priceRemote.calculatefbfrequency("15");
    }

    @Override
    public JsonResultVo sysDealerInfoService() {
        return priceRemote.sysDealerInfo("15");
    }
}
