package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.dto.MessageRunTimeJobDTO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.ep.quartz.service.JobService;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.ep.quartz.service.QuartzService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
@Tag(name = "定时任务", description = "定时任务")
@RestController
@RequestMapping("/quartz")
public class QuartzController extends BaseController {
    @Autowired
    private QuartzService quartzService;
    @Autowired
    private JobService taskScheduleJobService;
    @Autowired
    private QuartzLogService quartzLogService;
    @Autowired
    private I18nUtil i18nUtil;

    @Operation(summary = "列表查询", description = "[author:10027705]")
    @PostMapping("selectList")
    public JsonResultVo selectList(@RequestBody QuartzDTO dto) {
        JsonResultVo ret = new JsonResultVo();
        try {
            QmQueryWrapper<QuartzDO> qmQueryWrapper = new QmQueryWrapper<>();
            if (!BootAppUtil.isNullOrEmpty(dto.getJobGroup())) {
                qmQueryWrapper.like("jobGroup", dto.getJobGroup());
            }
            if (!BootAppUtil.isNullOrEmpty(dto.getJobName())) {
                qmQueryWrapper.like("jobName", dto.getJobName());
            }
            qmQueryWrapper.orderByDesc("id");
            QmPage<QuartzDO> list = quartzService.table(qmQueryWrapper, dto);
            ret.setData(list);
        } catch (Exception ex) {
            String message = i18nUtil.getMessage("ERR.quartz.common.exception");
            ret.setMsgErr(message + ex.toString());
        }
        return ret;
    }


    @Operation(summary = "保存", description = "[author:10027705]")
    @PostMapping("save")
    public JsonResultVo save(@RequestBody QuartzDO queryMessage) {
        JsonResultVo ret = new JsonResultVo();
        try {
            int res = 0;
            //添加到任务中------------------------------
            queryMessage.setJobStatus("1");
            if (BootAppUtil.isNullOrEmpty(queryMessage.getId())) {
                res = taskScheduleJobService.save(queryMessage);
            } else {
                res = taskScheduleJobService.update(queryMessage);
            }
            if (res > 0) {
                String message = i18nUtil.getMessage("MSG.quartz.common.operateSuccess");
                ret.setMsg(message);
            } else {
                String message = i18nUtil.getMessage("ERR.quartz.common.operateFail");
                ret.setMsgErr(message);
            }
        } catch (Exception ex) {
            String message = i18nUtil.getMessage("ERR.quartz.common.operateFail");
            ret.setMsgErr(message + ex.toString());
        }
        return ret;
    }

    @Operation(summary = "删除", description = "[author:10027705]")
    @GetMapping("delete")
    public JsonResultVo delete(@RequestParam String id) {
        JsonResultVo ret = new JsonResultVo();
        try {
            if (taskScheduleJobService.remove(id) > 0) {
                String message = i18nUtil.getMessage("MSG.quartz.common.delSuccess");
                ret.setMsg(message);
            } else {
                String message = i18nUtil.getMessage("ERR.quartz.common.delFail");
                ret.setMsgErr(message);
            }
        } catch (Exception ex) {
            String message = i18nUtil.getMessage("ERR.quartz.common.delFail");
            ret.setMsgErr(message);
        }
        return ret;
    }

    @Operation(summary = "查询唯一组", description = "[author:10027705]")
    @PostMapping("selectDistinctGroup")
    public JsonResultVo selectDistinctGroup() {
        JsonResultVo ret = new JsonResultVo();
        try {
            Map map = new HashMap();
            List<QuartzDO> list = quartzService.selectDistinctGroup(map);
            ret.setDataList(list);
        } catch (Exception ex) {
            String message = i18nUtil.getMessage("ERR.quartz.common.getFail");
            ret.setMsgErr(message + ex.getMessage());
        }
        return ret;
    }

    @Operation(summary = "查询唯一姓名", description = "[author:10027705]")
    @PostMapping("selectDistinctName")
    public JsonResultVo selectDistinctName() {
        JsonResultVo ret = new JsonResultVo();
        try {
            Map map = new HashMap();
            List<QuartzDO> list = quartzService.selectDistinctName(map);
            ret.setDataList(list);
        } catch (Exception ex) {
            String message = i18nUtil.getMessage("ERR.quartz.common.getFail");
            ret.setMsgErr(message + ex.getMessage());
        }
        return ret;
    }

    /**
     * 立即执行
     */
    @Operation(summary = "立即执行", description = "[author:10027705]")
    @PostMapping("/running")
    public JsonResultVo running(@RequestBody QuartzDO query) {
        String id = String.valueOf(query.getId());
        JsonResultVo resultObj = new JsonResultVo();
        try {
            if (taskScheduleJobService.running(id) > 0) {
                //保存日志
                QmQueryWrapper<QuartzDO> qmQueryWrapper = new QmQueryWrapper<>();
                qmQueryWrapper.eq("id", id);
                List<QuartzDO> quartzDOList = quartzService.list(qmQueryWrapper);
                if (!CollectionUtils.isEmpty(quartzDOList)) {
                    quartzLogService.saveLogByQuartzId(quartzDOList.get(0));
                }
                resultObj.setMsg("running OK!");
            } else {
                resultObj.setMsgErr("running ERROR");
            }
        } catch (Exception ex) {
            resultObj.setMsgErr("running ERROR:" + ex.getMessage(), ex);
        }
        return resultObj;
    }


    /**
     * 执行统计
     */
    @Operation(summary = "执行统计", description = "[author:10027705]")
    @PostMapping("/executionStatistics")
    public JsonResultVo executionStatistics(@RequestBody QuartzDTO dto) {
        JsonResultVo resultObj = new JsonResultVo();
        if (BootAppUtil.isNullOrEmpty(dto.getStartTime())) {
            String message = i18nUtil.getMessage("ERR.quartz.common.startTimeNull");
            resultObj.setMsgErr(message);
        }
        if (BootAppUtil.isNullOrEmpty(dto.getEndTime())) {
            String message = i18nUtil.getMessage("ERR.quartz.common.endTimeNull");
            resultObj.setMsgErr(message);
        }
        resultObj.setData(quartzService.executionStatistics(dto));
        return resultObj;
    }

    /**
     * 带分页的执行统计
     *
     * @param dto
     * @return
     */
    @Operation(summary = "带分页的执行统计", description = "[author:10027705]")
    @PostMapping("/executionStatisticsQuery")
    public JsonResultVo<QmPage<QuartzStatisticsVO>> executionStatisticsQuery(@RequestBody QuartzDTO dto) {
        JsonResultVo<QmPage<QuartzStatisticsVO>> resultVo = new JsonResultVo<>();
        QmPage<QuartzStatisticsVO> list = quartzService.executionStatisticsQuery(dto);
        resultVo.setData(list);
        return resultVo;
    }


    @Operation(summary = "获取运行信息", description = "[author:10027705]")
    @PostMapping("/getMessageRunTimeJobListForRun")
    public JsonResultVo<List<MessageRunTimeJobPO>> getMessageRunTimeJobListForRun(@RequestBody MessageRunTimeJobDTO messageRunTimeJobDTO) {
        JsonResultVo<List<MessageRunTimeJobPO>> resultVo
                = quartzService.getMessageRunTimeJobListForRun(messageRunTimeJobDTO);
        return resultVo;
    }

    @Operation(summary = "发生通知", description = "[author:10027705]")
    @PostMapping("/sendNoticeJob")
    public JsonResultVo<List<MessageRunTimeJobPO>> sendNoticeJob() {
        JsonResultVo<List<MessageRunTimeJobPO>> resultVo = quartzService.sendNoticeJob();
        return resultVo;
    }
}
