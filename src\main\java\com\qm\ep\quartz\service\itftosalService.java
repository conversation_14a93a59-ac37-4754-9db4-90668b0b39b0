package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface itftosalService {


    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    JsonResultVo<Object> dealsysc060list(String loginKey);

    /**
     * 定时任务 -省市区县sysc005同步
     */
    JsonResultVo<Object> dealsysc005list(String loginKey);


    /**
     * 定时任务 -人员授权公司sysi051同步
     */
    JsonResultVo<Object> dealsysi051list(String loginKey);

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    JsonResultVo<Object> dealsysc009dlist(String loginKey);

    /**
     * 定时任务 -mdac001c同步
     */
    JsonResultVo<Object> dealmdac001clist(String loginKey);

    /**
     * 定时任务 -mdac001d同步
     */
    JsonResultVo<Object> dealmdac001dlist(String loginKey);

    /**
     * 定时任务 -mdac001p同步
     */
    JsonResultVo<Object> dealmdac001plist(String loginKey);

    /**
     * 定时任务 -mdac001r同步
     */
    JsonResultVo<Object> dealmdac001rlist(String loginKey);

    /**
     * 定时任务 -mdac002同步
     */
    JsonResultVo<Object> dealmdac002list(String loginKey);

    /**
     * 定时任务 -mdac002d同步
     */
    JsonResultVo<Object> dealmdac002dlist(String loginKey);

    /**
     * 定时任务 -mdac009同步
     */
    JsonResultVo<Object> dealmdac009list(String loginKey);

    /**
     * 定时任务 -mdac009d同步
     */
    JsonResultVo<Object> dealmdac009dlist(String loginKey);

    /**
     * 定时任务 -mdac600同步
     */
    JsonResultVo<Object> dealmdac600list(String loginKey);

    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步(同步sc_vc库)
     */
    JsonResultVo<Object> dealsysc060toscvclist(String loginKey);


}
