package com.qm.ep.quartz.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
public class SearchPriceDTO {

    @Schema(title = "价格策略", description = "价格策略")
    private String npricestrategy;

    @Schema(title = "价格策略id", description = "价格策略id")
    private String strategyId;

    @Schema(title = "日期", description = "日期")
    private LocalDate date;
}
