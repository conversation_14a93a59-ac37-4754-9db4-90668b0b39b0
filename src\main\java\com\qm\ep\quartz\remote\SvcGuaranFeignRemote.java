package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 投诉单定时生成技术服务申请单
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-svc-guarantee", fallbackFactory = SvcGuaranFeignFallbackFactory.class)
public interface SvcGuaranFeignRemote {

    /**
     *投诉单定时生成技术服务申请单
     */
    @PostMapping("/complainDealBill/tsTimingTask")
    JsonResultVo<Object> tsTimingTask(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/fFVComplaintImport/ffVComplaintBill")
    JsonResultVo<Object> ffVComplaintBill(@RequestHeader("tenantId") String tenantId);

    /**
     *整车退回定时生成技术服务申请单
     */
    @PostMapping("/rCVChargeBill/tsTimingBill")
    JsonResultVo<Object> tsTimingBill(@RequestHeader("tenantId") String tenantId,@RequestBody LoginKeyDO loginKeyDO);

    /**
     * 联拖车救援申请单号的服务支援单提交之后一审和二审直接通过
     */
    @PostMapping("/serviceSupportBill/serviceSupportAutoReview")
    JsonResultVo<Object> serviceSupportAutoReview(@RequestHeader("tenantId") String tenantId);


    /**
     * 政策保养自动审核
     */
    @PostMapping("/policyMntnBill/autoAdt")
    JsonResultVo<Object> policyMntnAutoReview(@RequestHeader("tenantId") String tenantId,@RequestBody LoginKeyDO loginKeyDO);

    /**
     * 获取供应商自动回复数据
     */
    @PostMapping("/marketFeedbackBill/vdrAutoReply")
    JsonResultVo<Object> vdrAutoReply(@RequestHeader("tenantId") String tenantId,@RequestBody LoginKeyDO loginKeyDO);

}
