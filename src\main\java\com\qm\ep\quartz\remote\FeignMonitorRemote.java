package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-svc-monitor", fallbackFactory = FeignFallbackFactory.class)
public interface FeignMonitorRemote {

    /**
     * 计算所有经销商的上月平均分
     */
    @PostMapping("/monitorTask/distributorAverageLastOneMonth")
    JsonResultVo<Boolean> distributorAverageLastOneMonth(@RequestHeader("tenantId") String tenantId);

    /**
     * 计算所有经销商的前12月平均分
     */
    @PostMapping("/monitorTask/distributorAverageLastTwelveMonth")
    JsonResultVo<Boolean> distributorAverageLastTwelveMonth(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/sVCBusSnToRules/doBRScene")
    JsonResultVo<Boolean> doBRScene(@RequestHeader("tenantId") String tenantId, @RequestParam("vBillType") String vBillType, @RequestParam("vSvcRBNode") String vSvcRBNode, @RequestParam("nBillId") String nBillId, @RequestParam("companyid") String companyid);


    /**
     * 未机审索赔单预审（机审后调规则库部分）
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/clmBill/doBRScene4NoSampledClmBill")
    JsonResultVo<Boolean> doBRScene4NoSampledClmBill(@RequestHeader("tenantId") String tenantId);
}
