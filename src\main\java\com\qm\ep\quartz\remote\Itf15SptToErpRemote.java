package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToErpFallbackFactory.class)
public interface Itf15SptToErpRemote {

    /**
     * 定时任务 - 001 SptToErp：传输下线入库数据
     */
    @PostMapping("/itf15SptToErp/transferDataOferp001")
    JsonResultVo<Object> transferDataOferp001(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 002 SptToErp：传输下线入库数据
     */
    @PostMapping("/itf15SptToErp/transferDataOferp002")
    JsonResultVo<Object> transferDataOferp002(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 003 SptToErp：把下线入库数据插到ep中间表
     */
    @PostMapping("/itf15SptToErp/insertMiddleTableOfErp001")
    JsonResultVo<Object> insertMiddleTableOfErp001(@RequestHeader("companyId") String companyId,
                                                   @RequestHeader("custGroupId") String custGroupId,
                                                   @RequestHeader("tenantId") String tenantId,
                                                   @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 004 SptToErp：把出库数据插到ep中间表
     */
    @PostMapping("/itf15SptToErp/insertMiddleTableOfErp001")
    JsonResultVo<Object> insertMiddleTableOfErp002(@RequestHeader("companyId") String companyId,
                                                   @RequestHeader("custGroupId") String custGroupId,
                                                   @RequestHeader("tenantId") String tenantId,
                                                   @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 005 SptToErp：把下线入库数据从ep中间表插入epr中间表
     */
    @PostMapping("/itf15SptToErp/transferToErp001")
    JsonResultVo<Object> transferToErp001(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 006 SptToErp：把出库数据从ep中间表插入epr中间表
     */
    @PostMapping("/itf15SptToErp/transferToErp002")
    JsonResultVo<Object> transferToErp002(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

}
