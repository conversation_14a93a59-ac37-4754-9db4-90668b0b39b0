package com.qm.ep.quartz;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableHystrix
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = "com.qm")
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan({"com.qm.tds.base.mapper", "com.qm.ep.quartz.mapper"})
public class CommonQuartzApplication {

    public static void main(String[] args) {
        SpringApplication.run(CommonQuartzApplication.class, args);
    }
}
