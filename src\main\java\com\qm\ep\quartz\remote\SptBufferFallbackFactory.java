package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - Spt 缓存直发车辆自动锁车功能
 */

@Component
@Slf4j
public class SptBufferFallbackFactory implements FallbackFactory<SptBufferRemote> {

    @Override
    public SptBufferRemote create(Throwable throwable) {
        SptBufferRemoteHystrix hystrix = new SptBufferRemoteHystrix();
        hystrix.setHystrixEx(throwable);
        return hystrix;
    }
}
