package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ActQuotaFeignRemote;
import com.qm.ep.quartz.service.ActQuotaService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * ClassName:ActQuotaServiceImpl
 * package:com.qm.ep.quartz.service.impl
 * Description:
 *
 * @Date:2021/6/15 14:06
 * @Author:sunyu
 */

@Slf4j
@Service
public class ActQuotaServiceImpl implements ActQuotaService {
    @Autowired
    private ActQuotaFeignRemote actQuotaFeignRemote;

    /**
     * 定时任务额度测算
     */
    @Override
    public JsonResultVo actQuota() {

        SimpleDateFormat yy = new SimpleDateFormat("yyyy");
        SimpleDateFormat mm = new SimpleDateFormat("MM");

        Date date = new Date();

        String year = yy.format(date);
        String month = mm.format(date);

        String ny = year+month;

        return actQuotaFeignRemote.actQuota("15",year, month);
    }
}
