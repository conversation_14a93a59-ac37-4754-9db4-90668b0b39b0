package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * @author: jianghong
 * @time: 2021/4/1 11:27
 */
public interface Itf15SptToVlmsService {
    /**
     * 定时任务 - SptToVlms：省区 001
     */
    JsonResultVo transferDataOfcsqdm(String loginKey);

    /**
     * 定时任务 - SptToVlms：市 002
     */
    JsonResultVo transferDataOfcsxdm(String loginKey);

    /**
     * 定时任务 - SptToVlms：区县 003
     */
    JsonResultVo transferDataOfcxqdm(String loginKey);

    /**
     * 定时任务 - SptToVlms：产品代码 004
     */
    JsonResultVo transferDataOfcpdm(String loginKey);

    /**
     * 定时任务 - SptToVlms：发运计划 005
     */
    JsonResultVo transferDataOffyjh(String loginKey);

    /**
     * 定时任务 - SptToVlms：删除发运计划 006
     */
    JsonResultVo transferDataOftfyjh(String loginKey);

    /**
     * 定时任务 - SptToVlms：指派车道 007
     */
    JsonResultVo transferDataOfzpcd(String loginKey);

    /**
     * 定时任务 - SptToVlms：取消指派车道 008
     */
    JsonResultVo transferDataOfqxzpcd(String loginKey);

    /**
     * 定时任务 - SptToVlms：发运出库 009
     */
    JsonResultVo transferDataOffyck(String loginKey);

    /**
     * 定时任务 - SptToVlms：出库换车 010
     */
    JsonResultVo transferDataOfckhc(String loginKey);

    /**
     * 定时任务 - SptToVlms：车辆到货 011
     */
    JsonResultVo transferDataOfcldh(String loginKey);

    /**
     * 定时任务 - SptToVlms：计划返单 012
     */
    JsonResultVo transferDataOfjhfd(String loginKey);

    /**
     * 定时任务 - SptToVlms：运费结算 013
     */
    JsonResultVo transferDataOfyfjs(String loginKey);

    /**
     * 定时任务 - SptToVlms：下线入库信息传给一汽物流接口
     */
    JsonResultVo transferDataOfWms(String loginKey);
}
