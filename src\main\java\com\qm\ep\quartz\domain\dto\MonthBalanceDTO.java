package com.qm.ep.quartz.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* <p>
* 备件月结时间
* </p>
* <AUTHOR>
* @since 2020-12-04
*/
@Schema(title = "备件月结时间", description = "备件月结时间")
@Data
public class MonthBalanceDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;

    private String companyId;


    private String thisYearMonth;

    private String nextYearMonth;

    private String thisYear;

    private String thisMonth;

    private String nextYear;

    private String nextMonth;

    private String flag;

    private String beginDate;

    private String endDate;

    private String nspa;

    private String nwh;

    private double onWayAmt;



}