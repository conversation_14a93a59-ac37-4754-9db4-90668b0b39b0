package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.SendSalb060ToV2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendSalb060ToV2Impl implements SendSalb060ToV2Service {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * ep向v2 定时同步 信用账户
     */
    @Override
    public JsonResultVo transferSalb060ToTdsv2(String batchSize){
        return financeTransferRemote.transferSalb060ToTdsv2("15","6000",batchSize);
    }
}
