package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CalculateStandardStockRemote;
import com.qm.ep.quartz.service.CalculateStandardStockService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CalculateStandardStockServiceImpl implements CalculateStandardStockService {

    @Autowired
    private CalculateStandardStockRemote feignRemote;

    @Override
    public JsonResultVo startScheduleBySSQ() {
        return feignRemote.startScheduleBySSQ("15");
    }

    @Override
    public JsonResultVo autoDistribNormal() {
        return feignRemote.autoDistribNormal("15");
    }

    @Override
    public JsonResultVo autoDistribUrgent() {
        return feignRemote.autoDistribUrgent("15");
    }

    @Override
    public JsonResultVo autoDistribCulturalCreation() {
        return feignRemote.autoDistribCulturalCreation("15");
    }

    @Override
    public JsonResultVo autoabolish() {
        return feignRemote.autoabolish("15");
    }

    @Override
    public JsonResultVo calculateSpar925() {
        return feignRemote.calculateSpar925("15");
    }

    @Override
    public JsonResultVo autoqueryrate() {
        return feignRemote.autoqueryrate("15");
    }

    @Override
    public JsonResultVo autoRenewRatio(String date) {
        return feignRemote.autoRenewRatio("15", date);
    }

    @Override
    public JsonResultVo autoServiceSupport(String date) {
        return feignRemote.autoServiceSupport("15", date);
    }

    @Override
    public JsonResultVo autoFFVComplaint(String date) {
        return feignRemote.autoFFVComplaint("15", date);
    }

    @Override
    public JsonResultVo summary() {
        return feignRemote.summary("15");
    }

    @Override
    public JsonResultVo<Object> getAak(String params) {
        return feignRemote.getAak("15", params);
    }

    @Override
    public JsonResultVo<Object> getRecording(String params) {
        return feignRemote.getRecording("15", params);
    }

    @Override
    public JsonResultVo autoDistribCulturalNew() {
        return feignRemote.autoDistribCulturalNew("15");
    }

    @Override
    public JsonResultVo autoDistribCulturalNew2() {
        return feignRemote.autoDistribCulturalNew2("15");
    }

    @Override
    public JsonResultVo autoDistribCulturalNew22() {
        return feignRemote.autoDistribCulturalNew22("15");
    }
}
