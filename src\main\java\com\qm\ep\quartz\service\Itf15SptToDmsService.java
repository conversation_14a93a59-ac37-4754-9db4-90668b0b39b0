package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * dms采购入库，采购退库接口
 *
 * @author: jianghong
 * @time: 2021/5/25 8:12
 */
public interface Itf15SptToDmsService {
    /**
     * 定时任务 - 001 SptToDms：传输采购入库数据
     */
    JsonResultVo<Object> purchaseConfirmEPAsyn(String loginKey);

    /**
     * 定时任务 - 002 SptToDms：传输采购退库数据
     */
    JsonResultVo<Object> purchaseReturnEPAsyn(String loginKey);

    /**
     * 定时任务 - 003 DmsToSpt：从dms获取到货确认数据并自动确认
     */

     JsonResultVo<Object> getPurchaseConfirmEPListAndConfirm(String loginKey);
}
