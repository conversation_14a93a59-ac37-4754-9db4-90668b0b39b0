package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @ClassName : V2接收EP客户信息
 * @Description :
 * <AUTHOR> wang<PERSON>
 * @Date: 2021-04-23  9:42
 */
@Component
public class ReceiveMdac301FallbackFactory implements FallbackFactory<ReceiveMdac301FromEPRemote> {

    @Override
    public ReceiveMdac301FromEPRemote create(Throwable throwable) {
        ReceiveMdac301FromEPRemoteHystrix hystrix = new ReceiveMdac301FromEPRemoteHystrix();
        hystrix.setHystrixEx(throwable);
        return hystrix;
    }
}
