package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ChandaoApiFallbackFactory implements FallbackFactory<ChandaoApiRemote> {
    @Override
    public ChandaoApiRemote create(Throwable throwable) {
        ChandaoApiRemoteHystrix chandaoApiRemoteHystrix = new ChandaoApiRemoteHystrix();
        chandaoApiRemoteHystrix.setHystrixEx(throwable);
        return chandaoApiRemoteHystrix;
    }
}
