package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.saveMarketPriceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class saveMarketPriceImpl implements saveMarketPriceService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * 更新市场指导价
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo saveMarketPrice() {
        return financeTransferRemote.saveMarketPrice("15", "6000");
    }
}
