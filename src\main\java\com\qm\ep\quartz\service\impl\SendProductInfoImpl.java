package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.SendProductInfoRemote;
import com.qm.ep.quartz.remote.itftosalRemote;
import com.qm.ep.quartz.service.SendProductInfoService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendProductInfoImpl implements SendProductInfoService {

    @Autowired
    private SendProductInfoRemote sendProductInfoRemote;
    @Autowired
    com.qm.ep.quartz.remote.itftosalRemote itftosalRemote;
    @Override
    public JsonResultVo sendProductInfoToDCEP() {
        return sendProductInfoRemote.SendProductInfo("15", "6000");
    }

    //向DMS发送产品信息
    @Override
    public JsonResultVo sendProductInfoToDMS() {
        return sendProductInfoRemote.sendProductInfoToDMS("15", "6000");
    }

    /**
     * 红旗H9产品信息与电商中心接口
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo
     * @Author: 刘伟新
     * @Date: 2021/5/10
     */
    @Override
    public JsonResultVo transferProductToDS() {
        return sendProductInfoRemote.transferProductToDS("15", "6000");
    }


    /**
     * 整车所有产品选装包拆分
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo
     * @Author: yanghuan
     * @Date: 2024/12/12
     */
    @Override
    public JsonResultVo calculateProductPacData() {
        return sendProductInfoRemote.calculateProductPacData("15", "6000");
    }


    /**
     * 根据FBOM获得年款车型后主动拉取FBOM得配置信息
     */
    @Override
    public JsonResultVo sendSalesConfigCode() {
        return sendProductInfoRemote.sendSalesConfigCode("15", "6000");
    }

    @Override
    public JsonResultVo<Object> dealsysc009dlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return sendProductInfoRemote.dealsysc009dlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }

    /**
     * 给dms同步字典
     *
     * @return
     */
    @Override
    public JsonResultVo<Boolean> sendDictToDms() {
        return sendProductInfoRemote.sendDictToDms("15", "6000");
    }

    /**
     * 给衍生金融同步经销商
     * @return
     */
    @Override
    public JsonResultVo<Boolean> sendDealerInfoToUtFin() {
        return sendProductInfoRemote.sendDealerInfoToUtFin("15", "6000");
    }

    /**
     *给衍生金融同步产品
     * @return
     */
    @Override
    public JsonResultVo<Boolean> sendProductInfoToUtFin() {
        return sendProductInfoRemote.sendProductInfoToUtFin("15", "6000");
    }

    /**
    * @Description: 定时同步tdsv2产品接口数据
    * @Param: [vJson]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/4/27
    */
    @Override
    public JsonResultVo<Object> sendTdsV2mdac008c(String vJson) {
        return sendProductInfoRemote.sendTdsV2mdac008c("15", "6000",vJson);
    }

    @Override
    public JsonResultVo sendProductPriceToDSZX() {
        return itftosalRemote.sendProductPriceToDSZX("15", "6000");
    }

}
