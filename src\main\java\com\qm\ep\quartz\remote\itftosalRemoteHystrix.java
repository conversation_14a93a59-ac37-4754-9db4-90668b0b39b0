package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 定时任务 - 融资余额自动转款
 */
@Component
@Slf4j
public class itftosalRemoteHystrix extends QmRemoteHystrix<itftosalRemote> implements itftosalRemote {
    private static final String MESSAGE = " 接口服务维护中请耐心等待.........";


    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @Override
    public JsonResultVo<Object> dealsysc060list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "sysc060、sysc060d1、sysc060d2同步dealsysc060list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -省市区县sysc005同步
     */
    @Override
    public JsonResultVo<Object> dealsysc005list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "省市区县sysc005同步dealsysc005list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    /**
     * 定时任务 -人员授权公司sysi051同步
     */
    @Override
    public JsonResultVo<Object> dealsysi051list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "人员sysc030同步dealsysi051list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    @Override
    public JsonResultVo<Object> dealsysc009dlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "字典子表sysc009d同步dealsysc009dlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac001c同步
     */
    @Override
    public JsonResultVo<Object> dealmdac001clist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac001c同步dealmdac001clist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac001d同步
     */
    @Override
    public JsonResultVo<Object> dealmdac001dlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac001d同步dealmdac001dlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac001p同步
     */
    @Override
    public JsonResultVo<Object> dealmdac001plist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac001p同步dealmdac001plist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac001r同步
     */
    @Override
    public JsonResultVo<Object> dealmdac001rlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac001r同步dealmdac001rlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac002同步
     */
    @Override
    public JsonResultVo<Object> dealmdac002list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "mdac002同步dealmdac002list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac002d同步
     */
    @Override
    public JsonResultVo<Object> dealmdac002dlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac002d同步dealmdac002dlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    /**
     * 定时任务 -mdac009同步
     */
    @Override
    public JsonResultVo<Object> dealmdac009list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "mdac009同步dealmdac009list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac009d同步
     */
    @Override
    public JsonResultVo<Object> dealmdac009dlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "mdac009d同步dealmdac009dlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -mdac600同步
     */
    @Override
    public JsonResultVo<Object> dealmdac600list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "mdac600同步dealmdac600list".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -自动形成提车单
     */
    @Override
    public JsonResultVo<Boolean> autoFormOrder(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "自动形成提车单".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -自动形成提车单
     */
    @Override
    public JsonResultVo<Boolean> autoFormOrderIwork(String companyId,
                                               String custGroupId,
                                               String tenantId,
                                               String userId) {
        String errorMsg = "自动形成提车单".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 -周订单指派 自动形成提车单
     */
    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb008e(String companyId,
                                               String custGroupId,
                                               String tenantId,
                                               String userId) {
        String errorMsg = "周订单指派-自动形成提车单".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 -自动形成提车单-试驾车
     */
    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb23503(String companyId,
                                                           String custGroupId,
                                                           String tenantId,
                                                           String userId) {
        String errorMsg = "试驾车-自动形成提车单".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 -自动形成提车单-服务车
     */
    @Override
    public JsonResultVo<Boolean> autoFormOrderForSalb23507(String companyId,
                                                           String custGroupId,
                                                           String tenantId,
                                                           String userId) {
        String errorMsg = "服务车-自动形成提车单".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 -自动形成提车单-下线下发钉钉消息
     */
    @Override
    public JsonResultVo<Boolean> offlineDing(String companyId,
                                             String custGroupId,
                                             String tenantId,
                                             String userId) {
        String errorMsg = "otd下发钉钉消息".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 -自动形成提车单-48小时下线下发钉钉消息
     */
    @Override
    public JsonResultVo<Boolean> offlineBeforeTwoDays(String companyId,
                                                      String custGroupId,
                                                      String tenantId,
                                                      String userId) {
        String errorMsg = "otd48h下发钉钉消息".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 价格申请单-下发电商中心 市场指导价 2023.6.19
     */
    @Override
    public JsonResultVo<Boolean> sendProductPriceToDSZX(String companyId,
                                                        String tenantId) {
        String errorMsg = "下发电商中心市场指导价".concat(MESSAGE);
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


}
