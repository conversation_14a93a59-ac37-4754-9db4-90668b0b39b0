package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-fin", fallbackFactory = FinFallbackFactory.class)
public interface FinRemote {

    /**
     * 定时任务 - 向v2传送产品代码
     */
    @PostMapping("/mdac008C/itf_ep_mdac008c")
    JsonResultVo<List<Map<String, String>>> itf_ep_mdac008c();

}
