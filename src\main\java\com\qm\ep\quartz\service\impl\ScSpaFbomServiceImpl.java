package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ScSpaFeignRemote;
import com.qm.ep.quartz.service.ScSpaFbomService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ScSpaFbomServiceImpl implements ScSpaFbomService {

    @Autowired
    private ScSpaFeignRemote feignRemote;

    @Override
    public JsonResultVo scheduleFbomMasterial() {
        return feignRemote.scheduleFbomMasterial("15");
    }

    @Override
    public JsonResultVo relationsBetweenSpaAndProduct() {
        return feignRemote.relationsBetweenSpaAndProduct("15");
    }

    @Override
    public JsonResultVo spaClaimMsgService() {
        return feignRemote.generate("15");
    }

    @Override
    public JsonResultVo scheduleFbomVehicleSpa() {
        return feignRemote.scheduleFbomVehicleSpa("15");
    }

    @Override
    public JsonResultVo<Object> fbomProductTimesInterface() {
        return feignRemote.fbomProductTimesInterface("15");
    }


}
