package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcBatchFeignRemote;
import com.qm.ep.quartz.service.SvcBatchBlnService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SvcBatchBlnServiceImpl implements SvcBatchBlnService {
    @Autowired
    private SvcBatchFeignRemote feignRemote;

    @Override
    public JsonResultVo svcBatchTimingTask() {
        return feignRemote.svcBatchTimingTask("15");
    }

    @Override
    public JsonResultVo vdrBatchTimingTask() {
        return feignRemote.vdrBatchTimingTask("15");
    }

    @Override
    public JsonResultVo autoSvcInvc() {
        return feignRemote.autoSvcInvc("15");
    }

    public JsonResultVo sendSvcInvc2FinV6(String params) {
        Map<String,String> map=new HashMap<>();
        map.put("nBillId",params);
        return feignRemote.sendSvcInvc2FinV6("15",map);
    }
}

