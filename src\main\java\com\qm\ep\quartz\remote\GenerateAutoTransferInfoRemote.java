package com.qm.ep.quartz.remote;

/**
 * <AUTHOR>
 * @CreateTime 2020-09-30 8:33
 * @Version 1.0
 */

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-spa-manasal", fallbackFactory=GenerateAutoTransferInfoFallbackFactory.class)
public interface GenerateAutoTransferInfoRemote {

    @PostMapping("/transferBillInfo/generateAutoTransferData")
    JsonResultVo generateAutoTransferData(@RequestHeader("tenantId") String tenantId);


    @PostMapping("/transferBillInfo/deleteTemp")
    JsonResultVo deleteTemp(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/transferBillApply/autoDistributeByQuartz")
    JsonResultVo autoDistributeByQuartz(@RequestHeader("tenantId") String tenantId);

    /**
     *【EP->TDS】备件销售发票接口
     * @param tenantId
     * @return
     */
    @PostMapping("/spaSalInvcBillSub/syncSaleInvoiceToV6")
    JsonResultVo syncSaleInvoiceToV6(@RequestHeader("tenantId") String tenantId);
}
