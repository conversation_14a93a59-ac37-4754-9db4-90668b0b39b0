package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 计算ssq
 */

@Component
@Slf4j
public class WmsInterfaceFallbackFactory implements FallbackFactory<WmsInterfaceRemote> {
    /**

     */
    @Override
    public WmsInterfaceRemote create(Throwable cause) {
        WmsInterfaceRemoteHystrix wmsInterfaceRemoteHystrix = new WmsInterfaceRemoteHystrix();
        wmsInterfaceRemoteHystrix.setHystrixEx(cause);
        return wmsInterfaceRemoteHystrix;
    }

}
