package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.NfreQuenceRemote;
import com.qm.ep.quartz.service.NfreQuenceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NfreQuenceServiceImpl implements NfreQuenceService {

    @Autowired
    private NfreQuenceRemote nfreQuenceRemote;

    @Override
    public JsonResultVo nfreQuence() {
       return  nfreQuenceRemote.nfreQuence("15");
    }
}
