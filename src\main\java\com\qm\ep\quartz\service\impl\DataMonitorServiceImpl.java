package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.DataMonitorRemote;
import com.qm.ep.quartz.service.DataMonitorService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.I18nUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24
 */
@Service
public class DataMonitorServiceImpl implements DataMonitorService {

    @Autowired
    private DataMonitorRemote dataMonitorRemote;
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo monitorDataByVgroup1(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup2(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup3(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup4(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup5(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup6(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup7(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup8(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup9(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup10(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup11(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup12(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup13(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup14(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup15(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup16(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup17(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup18(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup19(String params) {
        return monitorDataByVgroup(params);
    }

    @Override
    public JsonResultVo monitorDataByVgroup20(String params) {
        return monitorDataByVgroup(params);
    }


    public JsonResultVo monitorDataByVgroup(String params) {
        if (StringUtils.isEmpty(params)) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.DataMonitorServiceImpl.monitorDataByVgroup");
            throw new QmException(errorMsg);
        }
        String[] strs = params.split(",");
        if (strs.length <= 2) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.DataMonitorServiceImpl.monitorDataByVgroup");
            throw new QmException(errorMsg);
        }
        return dataMonitorRemote.monitorDataByVgroup(strs[0], strs[1], strs[2]);
    }

}
