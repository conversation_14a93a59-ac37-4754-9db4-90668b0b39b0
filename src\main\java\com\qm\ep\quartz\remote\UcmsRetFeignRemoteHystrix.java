package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class UcmsRetFeignRemoteHystrix extends QmRemoteHystrix<UcmsRetFeignRemote> implements UcmsRetFeignRemote {

    @Override
    public JsonResultVo<Object> invoiceVerify(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> applyAccountTask(String tenantId,String companyId) {
        return getResult();
    }


}
