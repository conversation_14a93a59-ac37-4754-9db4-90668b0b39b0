package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 向DCEP传输车型车系
 */
@Repository
@FeignClient(name = "sc-service-product", fallbackFactory = SendProductInfoFallbackFactory.class)
public interface SendProductInfoRemote {

    /**
     * 定时任务 - 融资余额自动转款
     */
    @PostMapping("/interface/sendProductInfoToDCEP")
    JsonResultVo SendProductInfo(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 向fbom传送产品信息
     */
    @PostMapping("/interface/ProductSpectrum")
    JsonResultVo productSpectrum(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 补充处理产品信息
     */
    @PostMapping("/iF_FBOM_SAL_OUT_001/productInsert")
    JsonResultVo handleProductInfo(@RequestHeader("companyId") String companyId,
                                   @RequestHeader("custGroupId") String custGroupId,
                                   @RequestHeader("tenantId") String tenantId,
                                   @RequestHeader("userId") String userId);

    //向DMS发送产品信息
    @PostMapping("/interface/sendProductInfoToDMS")
    JsonResultVo sendProductInfoToDMS(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 红旗H9产品信息与电商中心接口
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo
     * @Author: 刘伟新
     * @Date: 2021/5/10
     */
    @PostMapping("/interface/transferProductToDS")
    JsonResultVo transferProductToDS(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务-拆分所有启用车系产品选装包数据
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo
     * @Author: yanghuan
     * @Date: 2024/12/12
     */
    @PostMapping("/interface/calculateProductPacData")
    JsonResultVo calculateProductPacData(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给TSP传输车型车系
     */
    @PostMapping("/interface/sendProductInfoForTsp")
    JsonResultVo sendProductInfoForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 根据FBOM获得年款车型后主动拉取FBOM得配置信息
     */
    @PostMapping("/interface/sendSalesConfigCode")
    JsonResultVo sendSalesConfigCode(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    @PostMapping("/sysc009d/dealsysc009dlist")
    JsonResultVo<Object> dealsysc009dlist(@RequestHeader("companyId") String companyId,
                                          @RequestHeader("custGroupId") String custGroupId,
                                          @RequestHeader("tenantId") String tenantId,
                                          @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 给DMS同步字典
     */
    @PostMapping("/sysc009d/sendDictToDms")
    JsonResultVo<Boolean> sendDictToDms(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 给衍生金融同步经销商
     *
     * @return
     */
    @PostMapping("/interface/sendDealerInfoToUtFin")
    JsonResultVo<Boolean> sendDealerInfoToUtFin(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 给衍生金融同步产品
     *
     * @return
     */
    @PostMapping("/interface/sendProductInfoToUtFin")
    JsonResultVo<Boolean> sendProductInfoToUtFin(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
    * @Description: 定时同步tdsv2产品接口数据
    * @Param: [vJson]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/4/27
    */
    @PostMapping("/pRODUCTMiddle/sendTdsV2mdac008c")
    JsonResultVo<Object> sendTdsV2mdac008c(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId,@RequestParam("vJson") String vJson);

    /**
    * @Description: 定时下发产品给三方接口
    * @Param: [tenantId, companyId]
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/6/2
    */
    @PostMapping("/interface/sendPoructInfaceMdac008")
    JsonResultVo<Object> sendPoructInfaceMdac008(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
