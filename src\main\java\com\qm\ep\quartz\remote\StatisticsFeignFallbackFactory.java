package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class StatisticsFeignFallbackFactory implements FallbackFactory<StatisticsFeignRemote> {

    @Override
    public StatisticsFeignRemote create(Throwable throwable) {
        StatisticsFeignRemoteHystrix feignRemoteHystrix = new StatisticsFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
