package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 二手车零售
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "uc-service-sel", fallbackFactory = FeignFallbackFactory.class)
public interface UcmsSelFeignRemote {

    /**
     * 定时任务自动下架
     * add by zhoul
     */
    @PostMapping("/sel1101/doOffAutoTask")
    JsonResultVo doOffAutoTask(@RequestHeader("tenantId") String tenantId);
}
