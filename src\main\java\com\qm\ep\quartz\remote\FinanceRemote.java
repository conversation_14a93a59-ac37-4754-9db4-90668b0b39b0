package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-fin", fallbackFactory = FinanceFallbackFactory.class)
public interface FinanceRemote {

    @PostMapping("/salb061/finance")
    JsonResultVo<Object> finance(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/FeignExeOut/out")
    JsonResultVo<Object> feignExeOut(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/FeignInvoice/invoice")
    JsonResultVo<Object> feignInvoice(@RequestHeader("tenantId") String tenantId);
}
