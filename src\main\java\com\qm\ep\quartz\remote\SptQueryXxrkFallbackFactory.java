package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SptQueryXxrkFallbackFactory implements FallbackFactory<SptQueryXxrkRemote> {

    @Override
    public SptQueryXxrkRemote create(Throwable throwable) {
        SptQueryXxrkHystrix SptQueryXxrkHystrix = new SptQueryXxrkHystrix();
        SptQueryXxrkHystrix.setHystrixEx(throwable);
        return SptQueryXxrkHystrix;
    }
}
