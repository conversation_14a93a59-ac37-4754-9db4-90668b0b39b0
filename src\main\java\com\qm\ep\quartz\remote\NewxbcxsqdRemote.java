package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**l
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "bt-service-sal", fallbackFactory = NewxbcxsqdFallbackFactory.class)
public interface NewxbcxsqdRemote {

    /**
     * 处理新保促销申请单状态
     */
    @PostMapping("/salb717/btsalb717")
    JsonResultVo btsalb717(@RequestHeader("tenantId") String tenantId);

    /**
     * 处理续保促销申请单状态
     */
    @PostMapping("/salb720/btsalb720")
    JsonResultVo btsalb720(@RequestHeader("tenantId") String tenantId);

    /**
     * 处理保险合伙人促销申请单状态
     */
    @PostMapping("/salb731/btsalb731")
    JsonResultVo btsalb731(@RequestHeader("tenantId") String tenantId);
}
