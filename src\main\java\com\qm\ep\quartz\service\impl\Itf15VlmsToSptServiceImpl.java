package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15VlmsToSptRemote;
import com.qm.ep.quartz.service.Itf15VlmsToSptService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * VlmsToSpt 接口
 *
 * @author: jianghong
 * @time: 2021/4/1 13:46
 */
@Service
public class Itf15VlmsToSptServiceImpl implements Itf15VlmsToSptService {
    @Autowired
    Itf15VlmsToSptRemote itf15VlmsToSptRemote;

    /**
     * 定时任务 - VlmsToSpt：司机信息 001
     */
    @Override
    public JsonResultVo transferDataOfsjxx(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfsjxx(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：运输车信息 002
     */
    @Override
    public JsonResultVo transferDataOfyscxx(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfyscxx(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003
     */
    @Override
    public JsonResultVo transferDataOfzpyss(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfzpyss(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - VlmsToSpt：指派运输商 003(月底执行)
     */
    @Override
    public JsonResultVo transferDataOfzpyss2(String loginKey) {
        LoginKeyDO loginKeyDO=this.getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfzpyss(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - VlmsToSpt：指派运输商 003(3\4状态补传)
     */
    @Override
    public JsonResultVo transferDataOfzpyss3(String loginKey) {
        LoginKeyDO loginKeyDO=this.getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfzpyss2(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - VlmsToSpt：取消指派运输商 004
     */
    @Override
    public JsonResultVo transferDataOfqxzpyss(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfqxzpyss(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：变更运输商 005
     */
    @Override
    public JsonResultVo transferDataOfbgyss(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfbgyss(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：打印提车单 006
     */
    @Override
    public JsonResultVo transferDataOfdytcd(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfdytcd(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：车辆位置信息 007
     */
    @Override
    public JsonResultVo transferDataOfgpszt(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfgpszt(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - VlmsToSpt：在途时间点 008
     */
    @Override
    public JsonResultVo transferDataOfsjztsj(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15VlmsToSptRemote.transferDataOfsjztsj(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
