package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24
 */
@Repository
@FeignClient(name = "tds-service-sys", fallbackFactory = DataMonitorFallbackFactory.class)
public interface DataMonitorRemote {

    @PostMapping("/data/monitor/vgroup")
    JsonResultVo monitorDataByVgroup(@RequestHeader("companyId") String companyId,@RequestHeader("tenantId") String tenantId, @RequestParam("vgroup") String vgroup);
}
