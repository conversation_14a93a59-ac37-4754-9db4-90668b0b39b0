package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class UTFinFeignRemoteHystrix extends QmRemoteHystrix<UTFinFeignRemote> implements UTFinFeignRemote {


    @Override
    public JsonResultVo updateTxdState(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo syncTxdStatus(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo txdEnterAccount(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo updateTxdTimestamp(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendInsPolicyToDms(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendTxdStateToDms(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo getInvoiceListUpdateDiscountDoc(String tenantId, String companyId) {
        return getResult();
    }
}
