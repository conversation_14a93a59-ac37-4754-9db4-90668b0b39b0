package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 车辆用途定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SvcCustFeignRemoteHystrix extends QmRemoteHystrix<SvcCustFeignRemote> implements SvcCustFeignRemote {

    @Override
    public JsonResultVo<Object> vehicleClassTimingTask(String tenantId) {
        return getResult();
    }


}
