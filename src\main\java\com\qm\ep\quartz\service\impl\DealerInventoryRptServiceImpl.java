package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.DealerInventoryRptRemote;
import com.qm.ep.quartz.service.DealerInventoryRptService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 经销商库存和提车在途日报生成服务
 * <AUTHOR>
 * @date 2021/7/9 13:21
 */
@Service
public class DealerInventoryRptServiceImpl implements DealerInventoryRptService {

    @Autowired
    private DealerInventoryRptRemote dealerInventoryRptRemote;
    /**
     * 生成经销商库存和提车库存日报
     * @return
     */
    @Override
    public JsonResultVo<Object> createInventoryRpt() {

        return dealerInventoryRptRemote.createInventoryRpt("15");

    }
}
