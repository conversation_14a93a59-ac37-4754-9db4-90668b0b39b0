package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */

@Component
@Slf4j
public class DeleteNullNwhFallbackFactory implements FallbackFactory<DeleteNullNwhRemote> {

    @Override
    public DeleteNullNwhRemote create(Throwable throwable) {
        DeleteNullNwhRemoteHystrix deletenullnwhremotehystrix = new DeleteNullNwhRemoteHystrix();
        deletenullnwhremotehystrix.setHystrixEx(throwable);
        return deletenullnwhremotehystrix;
    }
}
