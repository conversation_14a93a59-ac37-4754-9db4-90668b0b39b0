package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FeignCommonWfRemote;
import com.qm.ep.quartz.remote.FeignMonitorRemote;
import com.qm.ep.quartz.remote.SvcQAFeedbackBillFeignRemote;
import com.qm.ep.quartz.service.AutoMarketionService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AutoMarketionServiceImpl implements AutoMarketionService {

    @Autowired
    private SvcQAFeedbackBillFeignRemote svcQAFeedbackBillFeignRemote;
    @Autowired
    private FeignCommonWfRemote feignCommonWfRemote;

    @Autowired
    private FeignMonitorRemote feignRemote;

    @Override
    public JsonResultVo<Object> autoCreateMarketreport() {
        JsonResultVo<List<Map<String, String>>> resultVo = svcQAFeedbackBillFeignRemote.autoCreateMarketreport("15");
        return null;
    }

    @Override
    public JsonResultVo<Object> autoFirstExamineMarketreport(String params) {
        Map<String, String> map = new HashMap<>();
        map.put("limitCount", params);
        return svcQAFeedbackBillFeignRemote.autoFirstExamineMarketreport("15", map);
    }
}
