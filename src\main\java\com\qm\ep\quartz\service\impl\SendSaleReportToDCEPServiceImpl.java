package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendSaleReportToDCEPService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendSaleReportToDCEPServiceImpl implements SendSaleReportToDCEPService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendSaleReportToDCEP() {
        return customRemote.sendSaleReportToDCEP("15");
    }

}
