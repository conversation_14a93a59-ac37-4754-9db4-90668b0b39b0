package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.quartz.mapper.QuartzLogMapper;
import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SynchronousdealerService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class SynchronousdealerServiceImpl implements SynchronousdealerService {

    @Autowired
    private CustomRemote customRemote;
    @Autowired
    QuartzLogMapper quartzLogMapper;

    @Override
    public JsonResultVo synchdlrService(String loginKey) {
        String time = quartzLogMapper.getLastTime();
//        LoginKeyDO loginKeyDO=new LoginKeyDO();
        if(BootAppUtil.isNullOrEmpty(time)){
            time = DateUtils.getMinDate().toString();
        }
//        if (BootAppUtil.isnotNullOrEmpty(loginKey)){
//            loginKeyDO= JSONObject.parseObject(loginKey,LoginKeyDO.class);
//        }else{
//            loginKeyDO.setCompanyId("6000");
//            loginKeyDO.setTenantId("15");
//            loginKeyDO.setOperatorId("");
//            loginKeyDO.setCustGroupid("15");
//        }
        Map<String,String> timeM = new HashMap<>();
        timeM.put("time",time);
        timeM.put("loginKey",loginKey);
        return customRemote.synchronousDealer("15",timeM);
    }

    @Override
    public JsonResultVo updatedlrService() {
        String time = quartzLogMapper.getLastTime();
        if(BootAppUtil.isNullOrEmpty(time)){
            time = DateUtils.getMinDate().toString();
        }
        Map<String,String> timeM = new HashMap<>();
        timeM.put("time",time);

        return customRemote.updatedlr("15",timeM);
    }

}
