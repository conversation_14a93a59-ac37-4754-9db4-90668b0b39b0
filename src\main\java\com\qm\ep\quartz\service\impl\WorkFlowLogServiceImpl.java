package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ChandaoApiRemote;
import com.qm.ep.quartz.remote.WorkFlowLogRemote;
import com.qm.ep.quartz.service.ChandaoApiService;
import com.qm.ep.quartz.service.WorkFlowLogService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WorkFlowLogServiceImpl implements WorkFlowLogService {
    @Autowired
    private WorkFlowLogRemote workFlowLogRemote;

    @Override
    public JsonResultVo del() {
        return workFlowLogRemote.del();
    }
}
