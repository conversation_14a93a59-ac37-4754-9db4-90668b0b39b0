package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2021/10/9 16:02
 */
@Component
@Slf4j
public class SalSpcApplyFallbackFactory implements FallbackFactory<SalSpcApplyRemote> {
    @Override
    public SalSpcApplyRemote create(Throwable throwable) {
        SalSpcApplyRemoteHystrix feignRemoteHystrix = new SalSpcApplyRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
