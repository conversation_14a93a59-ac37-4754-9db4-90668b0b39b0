package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * l
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = FinanceTransferFallbackFactory.class)
public interface FinanceTransferRemote {

    /**
     * 定时任务 - 融资余额自动转款
     */
    @PostMapping("/salc107c/financeTransfer")
    JsonResultVo financeTransfer(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 售前PDI自动机审
     */
    @PostMapping("/salb091C/sendBillToV2")
    JsonResultVo sendBillToV2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 处理过期价格申请单
     */
    @PostMapping("/priceTableC/handleData")
    JsonResultVo handleData(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 补充提车单市场指导价
     */
    @PostMapping("/order/saveMarketPrice")
    JsonResultVo saveMarketPrice(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 试驾车预警发送钉钉消息
     */
    @PostMapping("/salb236/sendDingMessage")
    JsonResultVo sendDingMessage(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - ep向v2传送结算单
     */
    @PostMapping("/iTF15_SAL_TDSV2_BILL/sendSalb080ToV2")
    JsonResultVo sendSalb080ToV2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 月结作废库存调拨申请
     */
    @PostMapping("/salb230/yjvoid")
    JsonResultVo saveYjvoid(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给TSP传输车籍信息
     */
    @PostMapping("/interface/sendVehicleInfoForTsp")
    JsonResultVo sendVehicleInfoForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 给TSP传输车籍信息
     */
    @PostMapping("/interface/sendfailedVehicleInfoForTsp")
    JsonResultVo sendfailedVehicleInfoForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 向tsp传经销商档案
     */
    @PostMapping("/interface/sendDealerForTsp")
    JsonResultVo sendDealerForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - ep向v2传送信用账户
     */
    @PostMapping("/sALB060/transferSalb060ToTdsv2")
    JsonResultVo transferSalb060ToTdsv2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId,@RequestParam("batchSize") String batchSize);

    /**
     * 定时任务 - 试驾车申请单自动提交
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/salb235/testDriveApplySubmitHandle")
    JsonResultVo testDriveApplySubmitHandle(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 试驾车申请单自动聚合
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/salb235/testDriveApplyAggregationHandle")
    JsonResultVo testDriveApplyAggregationHandle(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
