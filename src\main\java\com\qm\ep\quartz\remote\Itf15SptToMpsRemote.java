package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToMps 储运和公安部系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToMpsFallbackFactory.class)
public interface Itf15SptToMpsRemote {

    /**
     * 定时任务 - 001 SptToMps：传输机动车终端销售信息数据
     */
    @PostMapping("/itf15_Spt_Mps_Outwh/transferDataOfMps001")
    JsonResultVo<Object> transferDataOfMps001(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);


}
