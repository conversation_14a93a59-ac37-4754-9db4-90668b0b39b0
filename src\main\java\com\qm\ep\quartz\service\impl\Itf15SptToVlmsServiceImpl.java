package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToVlmsRemote;
import com.qm.ep.quartz.service.Itf15SptToVlmsService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SptToVlms 接口
 *
 * @author: jianghong
 * @time: 2021/4/1 11:28
 */
@Service
public class Itf15SptToVlmsServiceImpl implements Itf15SptToVlmsService {
    @Autowired
    Itf15SptToVlmsRemote itf15SptToVlmsRemote;

    /**
     * 定时任务 - SptToVlms：省区 001
     */
    @Override
    public JsonResultVo transferDataOfcsqdm(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfcsqdm(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：市 002
     */
    @Override
    public JsonResultVo transferDataOfcsxdm(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfcsxdm(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：区县 003
     */
    @Override
    public JsonResultVo transferDataOfcxqdm(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfcxqdm(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：产品代码 004
     */
    @Override
    public JsonResultVo transferDataOfcpdm(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfcpdm(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：发运计划 005
     */
    @Override
    public JsonResultVo transferDataOffyjh(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOffyjh(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：删除发运计划 006
     */
    @Override
    public JsonResultVo transferDataOftfyjh(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOftfyjh(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：指派车道 007
     */
    @Override
    public JsonResultVo transferDataOfzpcd(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfzpcd(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo transferDataOfqxzpcd(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfqxzpcd(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：发运出库 009
     */
    @Override
    public JsonResultVo transferDataOffyck(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOffyck(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：出库换车 010
     */
    @Override
    public JsonResultVo transferDataOfckhc(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfckhc(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：车辆到货 011
     */
    @Override
    public JsonResultVo transferDataOfcldh(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfcldh(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：计划返单 012
     */
    @Override
    public JsonResultVo transferDataOfjhfd(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfjhfd(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：运费结算 013
     */
    @Override
    public JsonResultVo transferDataOfyfjs(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfyfjs(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - SptToVlms：下线入库信息传给一汽物流接口
     */
    @Override
    public JsonResultVo transferDataOfWms(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToVlmsRemote.transferDataOfWms(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
