package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CalculateStandardStockRemote;
import com.qm.ep.quartz.remote.FinanceRemote;
import com.qm.ep.quartz.service.FinanceService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName : FinanceServiceImpl
 * @Description :
 * <AUTHOR> WQS
 * @Date: 2021-03-27  10:10
 */
@Service
public class FinanceServiceImpl implements FinanceService {

    @Autowired
    FinanceRemote financeRemote;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    CalculateStandardStockRemote calculateStandardStockRemote;

    @Override
    public JsonResultVo<Object> finance() {
        JsonResultVo<Object> paravalue = calculateStandardStockRemote.paravalue("15");
        if (paravalue.isOk()) {
            if ("1".equals(paravalue.getData())) {
                return financeRemote.finance("15");
            } else {
                JsonResultVo<Object> resultVo = new JsonResultVo<>();
                String message = i18nUtil.getMessage("MSG.quartz.financeCommon.spareFinanceFlag");
                resultVo.setMsg(message);
                return resultVo;
            }
        } else {
            return paravalue;
        }
    }

    @Override
    public JsonResultVo<Object> feignExeOut() {
        JsonResultVo<Object> paravalue = calculateStandardStockRemote.paravalue("15");
        if (paravalue.isOk()) {
            if ("1".equals(paravalue.getData())) {
                return financeRemote.feignExeOut("15");
            } else {
                JsonResultVo<Object> resultVo = new JsonResultVo<>();
                String message = i18nUtil.getMessage("MSG.quartz.financeCommon.spareFinanceFlag");
                resultVo.setMsg(message);
                return resultVo;
            }
        } else {
            return paravalue;
        }
    }

    @Override
    public JsonResultVo<Object> invoice() {
        JsonResultVo<Object> paravalue = calculateStandardStockRemote.paravalue("15");
        if (paravalue.isOk()) {
            if ("1".equals(paravalue.getData())) {
                return financeRemote.feignInvoice("15");
            } else {
                JsonResultVo<Object> resultVo = new JsonResultVo<>();
                String message = i18nUtil.getMessage("MSG.quartz.financeCommon.spareFinanceFlag");
                resultVo.setMsg(message);
                return resultVo;
            }
        } else {
            return paravalue;
        }
    }
}
