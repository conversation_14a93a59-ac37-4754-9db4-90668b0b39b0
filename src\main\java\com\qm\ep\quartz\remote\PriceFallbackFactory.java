package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */

@Component
@Slf4j
public class PriceFallbackFactory implements FallbackFactory<PriceRemote> {

    @Override
    public PriceRemote create(Throwable throwable) {
        PriceRemoteHystrix priceremotehystrix = new PriceRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
