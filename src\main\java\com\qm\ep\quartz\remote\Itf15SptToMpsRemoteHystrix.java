package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToMps 储运和公安部系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToMpsRemoteHystrix extends QmRemoteHystrix<Itf15SptToMpsRemote> implements Itf15SptToMpsRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 SptToMps：传输机动车终端销售信息数据
     */
    @Override
    public JsonResultVo<Object> transferDataOfMps001(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToErp:transferDataOferp001".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


}
