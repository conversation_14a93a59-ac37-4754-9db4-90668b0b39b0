package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 批量更新索赔频次
 */

@Component
@Slf4j
public class NfreQuenceFallbackFactory implements FallbackFactory<NfreQuenceRemote> {

    @Override
    public NfreQuenceRemote create(Throwable throwable) {
        NfreQuenceRemoteHystrix nfrequenceremotehystrix = new NfreQuenceRemoteHystrix();
        nfrequenceremotehystrix.setHystrixEx(throwable);
        return nfrequenceremotehystrix;
    }
}
