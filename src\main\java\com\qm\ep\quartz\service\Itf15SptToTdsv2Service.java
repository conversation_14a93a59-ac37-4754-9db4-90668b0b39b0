package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * @author: jianghong
 * @time: 2021/4/22 18:29
 */

public interface Itf15SptToTdsv2Service {

    /**
     * 定时任务 - 001 SptToTdsv2：传输运费结算单数据
     */
    JsonResultVo transferDataOfSptb070CD(String loginKey);

    /**
     * 定时任务 - 002 SptToTdsv2：插入采购结算池数据(异步)
     */
    JsonResultVo transferDataOfInsertSalb046(String loginKey);
    /**
     * 定时任务 - 002 SptTov6：插入采购结算池数据(异步)
     */
    JsonResultVo transferDataOfInsertSalb046v6(String loginKey);

    /**
     * 定时任务 - 003 SptToTdsv2：删除采购结算池数据(异步)
     */
    JsonResultVo transferDataOfDeleteSalb046(String loginKey);
    /**
     * 定时任务 - 003 SptTov6：删除采购结算池数据(异步)
     */
    JsonResultVo transferDataOfDeleteSalb046v6(String loginKey);

    /**
     * 定时任务 - 提车出库  新能源 spt传给fin
     */
    JsonResultVo transferData(String loginKey);

    /**
     * 定时任务 - 提车出库  新能源 spt传给v6
     */
    JsonResultVo transferDatav6(String loginKey);
    /**
     * 定时任务 - 提车出库  新能源 spt传给fin  补传
     */
    JsonResultVo transferDataRepair(String loginKey);
}
