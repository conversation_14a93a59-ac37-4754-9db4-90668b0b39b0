package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class ScSpaFeignRemoteHystrix extends QmRemoteHystrix<ScSpaFeignRemote> implements ScSpaFeignRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务执行 Fbom物料接口处理
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> scheduleFbomMasterial(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.ScSpaFeignRemoteHystrix.scheduleFbomMasterial");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    @Override
    public JsonResultVo<Object> scheduleFbomVehicleSpa(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.ScSpaFeignRemoteHystrix.scheduleFbomVehicleSpa");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;

    }


    /**
     * 定时任务执行 Fbom获取产品与备件之间关系接口
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> relationsBetweenSpaAndProduct(String tenantId) {
//        String errorMsg="Fbom定时产品对应关系执行中请耐心等待.........";
//        JsonResultVo<Object> resultVo = new JsonResultVo<>();
//        resultVo.setCode(JsonResultVo.CODE_ERR);
//        resultVo.setMsgErr(errorMsg);
        return getResult();
    }


    @Override
    public JsonResultVo<Object> generate(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.ScSpaFeignRemoteHystrix.generate");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务执行 Fbom产品对应关系频次调用接口
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> fbomProductTimesInterface(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.ScSpaFeignRemoteHystrix.fbomProductTimesInterface");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> syncToTdsV2(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.ScSpaFeignRemoteHystrix.fbomProductTimesInterface");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
