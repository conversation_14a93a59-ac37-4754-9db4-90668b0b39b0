package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.base.domain.LoginKeyDO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 投诉单定时生成技术服务申请单
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SvcGuaranFeignRemoteHystrix extends QmRemoteHystrix<SvcGuaranFeignRemote> implements SvcGuaranFeignRemote {

    @Override
    public JsonResultVo<Object> tsTimingTask(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> ffVComplaintBill(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> tsTimingBill(String tenantId, LoginKeyDO loginKeyDO) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> serviceSupportAutoReview(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> policyMntnAutoReview(String tenantId,LoginKeyDO loginKeyDO) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> vdrAutoReply(String tenantId,LoginKeyDO loginKeyDO) {
        return getResult();
    }
}
