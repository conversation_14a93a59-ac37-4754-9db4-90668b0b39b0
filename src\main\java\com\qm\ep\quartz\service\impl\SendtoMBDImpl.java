package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendtoMBDRemote;
import com.qm.ep.quartz.service.SendtoMBDService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SendtoMBDImpl implements SendtoMBDService {

    @Autowired
    private SendtoMBDRemote sendtoMBDRemote;

    @Value("${EP.mbd}")
    private String dmsHost;

    @Override
    public JsonResultVo<Boolean> sendDbxxDataToMBD() {
        return sendtoMBDRemote.sendDbxxDataToMBD("15", "6000", dmsHost + "/api/timing/allot");
    }

    @Override
    public JsonResultVo<Boolean> sendLsapiDataToMBD() {
        return sendtoMBDRemote.sendLsapiDataToMBD("15", "6000", dmsHost + "/api/timing/allRetail");
    }

    @Override
    public JsonResultVo<Boolean> sendQbkcDataToMBD() {
        return sendtoMBDRemote.sendQbkcDataToMBD("15", "6000", dmsHost + "/api/timing/inventoryFinancing");
    }

    @Override
    public JsonResultVo<Boolean> sendRzhkDataToMBD() {
        return sendtoMBDRemote.sendRzhkDataToMBD("15", "6000", dmsHost + "/api/timing/financingPayments");
    }

    @Override
    public JsonResultVo<Boolean> sendScsjDataToMBD() {
        return sendtoMBDRemote.sendScsjDataToMBD("15", "6000", dmsHost + "/api/timing/driveDealers");
    }
}
