package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动把没维护供应商服务页的插入服务页
 */

@Component
@Slf4j
public class DemageFallbackFactory implements FallbackFactory<DemageRemote> {

    @Override
    public DemageRemote create(Throwable throwable) {
        DemageRemoteHystrix demageRemoteHystrix = new DemageRemoteHystrix();
        demageRemoteHystrix.setHystrixEx(throwable);
        return demageRemoteHystrix;
    }
}
