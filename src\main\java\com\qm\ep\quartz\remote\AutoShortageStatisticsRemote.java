package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-spa-query", fallbackFactory = AutoShortageStatisticsFallbackFactory.class)
public interface AutoShortageStatisticsRemote {
    /**
     * 定时任务 - 缺货统计汇总
     */
    @PostMapping("/spaShortageStatistics/AutoShortageStatistics")
    JsonResultVo<Object> AutoShortageStatistics(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/spaSalesStatistics/autoSummarizesStatisticsData")
    JsonResultVo<Object> autoSummarizesStatisticsData(@RequestHeader("tenantId") String tenantId);

    // 采购订单跟踪
    @PostMapping("/purPlanTrack/generateCgddGzData")
    JsonResultVo<Object> generateCgddGzData(@RequestHeader("tenantId") String tenantId);
}
