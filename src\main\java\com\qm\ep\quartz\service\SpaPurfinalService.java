package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface SpaPurfinalService {
    // 采购合同终审定时任务
    JsonResultVo PurstartBySSQ();
    // 形成400c/d采购订单定时任务
    JsonResultVo AutoFormSaleOrder();
    // 定时任务维护收货通知单 申请作废 超过24小时短信发送
    JsonResultVo sendSMSByTwentyFourHours();
    // 定时任务维护收货通知单 申请作废 超过48小时短信发送
    JsonResultVo sendSMSByFortyEightHours();
    // 定时任务新采购算法，定时加工销售订单 nbo 和 移库出库Nbo  删除临时表 spawnbo1 和 spawnbo2
    JsonResultVo newAlgorithmCalculateNbo();

}
