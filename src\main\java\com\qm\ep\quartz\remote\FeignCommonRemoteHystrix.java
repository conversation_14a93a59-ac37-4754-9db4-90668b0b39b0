package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.util.I18nUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/09/14 下午 6:28
 * @Description
 * @since 1.0
 */
public class FeignCommonRemoteHystrix extends QmRemoteHystrix<FeignCommonWfRemote> implements FeignCommonWfRemote {

    @Override
    public JsonResultVo startBizWorkFlow(String businessKey, String wfCode, Map<String, Object> wfInfo) {
        JsonResultVo jsonResultVo = new JsonResultVo<>();
        I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        String errorMsg = i18nUtil.getMessage("ERR.quartz.FeignCommonRemoteHystrix.common");
        jsonResultVo.setMsgErr(errorMsg);
        return jsonResultVo;
    }

    @Override
    public JsonResultVo completeByButton(Map<String, Object> requestParams) {
        JsonResultVo jsonResultVo = new JsonResultVo<>();
        I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
        String errorMsg = i18nUtil.getMessage("ERR.quartz.FeignCommonRemoteHystrix.common");
        jsonResultVo.setMsgErr(errorMsg);
        return jsonResultVo;
    }
}
