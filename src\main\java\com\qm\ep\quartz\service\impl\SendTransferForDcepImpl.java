package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendTransferForDcepService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendTransferForDcepImpl implements SendTransferForDcepService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo<String> sendnewVehicleTransferForDcep() {
        return customRemote.sendnewVehicleTransferForDcep("15", "6000");
    }


}
