package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Repository
@FeignClient(
        name = "common-service-mq",
        fallbackFactory = FeignCommonMQFallbackFactory.class
)
public interface FeignCommonMQRemote {
    @PostMapping({"/message/sendMQRetryTask"})
    JsonResultVo sendMQRetryTask();
}
