package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToDmsRemote;
import com.qm.ep.quartz.service.Itf15SptToDmsService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jianghong *
 * @time: 2021/5/25 8:40
 */
@Service
public class Itf15SptToDmsServiceImpl implements Itf15SptToDmsService {

    @Autowired
    Itf15SptToDmsRemote itf15SptToDmsRemote;

    /**
     * 定时任务 - 001 SptToDms：传输采购入库数据
     */
    @Override
    public JsonResultVo<Object> purchaseConfirmEPAsyn(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToDmsRemote.purchaseConfirmEPAsyn(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 002 SptToDms：传输采购退库数据
     */
    @Override
    public JsonResultVo<Object> purchaseReturnEPAsyn(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToDmsRemote.purchaseReturnEPAsyn(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("");
        }
        return loginKeyDO;
    }


    /**
     * 定时任务 - 003 DmsToSpt：从dms获取到货确认数据并自动确认
     */
    @Override
    public JsonResultVo<Object> getPurchaseConfirmEPListAndConfirm(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToDmsRemote.executeItf15_spt_dms_salb027_failure(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
}
