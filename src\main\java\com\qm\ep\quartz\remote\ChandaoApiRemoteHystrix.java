package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class ChandaoApiRemoteHystrix extends QmRemoteHystrix<ChandaoApiRemote> implements ChandaoApiRemote{

    @Override
    public JsonResultVo<Object> toUpdateAll() {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> taskToUpdateAll() {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> clearEsIndex(String specialServiceName) {
        return getResult();
    }
}
