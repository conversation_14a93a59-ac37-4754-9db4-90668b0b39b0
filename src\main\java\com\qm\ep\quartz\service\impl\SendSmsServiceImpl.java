package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.remote.FeignTempleRemote;
import com.qm.ep.quartz.service.SendSmsService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/8
 */
@Component
public class SendSmsServiceImpl implements SendSmsService {
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private FeignTempleRemote feignTempleRemote;
    // 伪代码，将Feign接口准入

    @Override
    public JsonResultVo sendEMail() {
        // return xxxx.sendEMail();

        return null;
    }

    @Override
    public JsonResultVo<List<MessageRunTimeJobPO>> sendEMail(String userInfo) {
        JsonResultVo<List<MessageRunTimeJobPO>> resultVo = new JsonResultVo<>();
        String[] info = null;
        if (StringUtils.isNotBlank(userInfo)) {
            info = userInfo.split(",");
            if (info.length == 3) {
                String vTenantId = info[0];
                String vCompanyId = info[1];
                String vOperatorId = info[2];
                resultVo = feignTempleRemote.sendNoticeJob(vTenantId, vCompanyId, vOperatorId);
                String errorMsg = i18nUtil.getMessage("MSG.quartz.SendSmsServiceImpl.sendEMailFinish");
                resultVo.setMsg(errorMsg);
            } else {
                String errorMsg = i18nUtil.getMessage("ERR.quartz.SendSmsServiceImpl.sendEMailCheckParam");
                resultVo.setMsgErr(errorMsg);
            }
        } else {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.SendSmsServiceImpl.sendEMailParamEmpty");
            resultVo.setMsgErr(errorMsg);
        }
        return resultVo;
    }

}
