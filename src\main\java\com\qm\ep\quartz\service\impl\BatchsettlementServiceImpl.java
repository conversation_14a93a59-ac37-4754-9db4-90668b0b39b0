package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CalculateStandardStockRemote;
import com.qm.ep.quartz.service.BatchsettlementService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName : BatchsettlementImpl
 * @Description : 定时批量结算
 * <AUTHOR> WQS
 * @Date: 2021-04-22  16:31
 */
@Service
public class BatchsettlementServiceImpl implements BatchsettlementService {

    @Autowired
    private CalculateStandardStockRemote calculateStandardStockRemote;

    @Override
    public JsonResultVo<Object> timedTask() {
        return calculateStandardStockRemote.timedTask("15");
    }
}
