package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-svc-flautspa", fallbackFactory = DeleteNullNwhFallbackFactory.class)
public interface DeleteNullNwhRemote {

    /**
     * 定时任务 - 自动生成价格
     */
    @PostMapping("/faultSpaPutinStorage/deleteNullNwh")
    JsonResultVo generate(@RequestHeader("tenantId") String tenantId);

}
