package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.remote.SalSpcApplyRemote;
import com.qm.ep.quartz.service.SalSpcApplyService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date ：2021/10/9 16:17
 */
@Service
public class SalSpcApplyServiceImpl implements SalSpcApplyService {
    @Autowired
    private SalSpcApplyRemote salSpcApplyRemote;
    /**
     * 大客户报备申请作废（作废）
     * @return
     */
    @Override
    public JsonResultVo voidSpcApply() {
        return salSpcApplyRemote.voidSpcApply("15", "6000");
    }
}
