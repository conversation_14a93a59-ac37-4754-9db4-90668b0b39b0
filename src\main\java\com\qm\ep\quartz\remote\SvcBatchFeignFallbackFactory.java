package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 服务批量结算定时任务 服务批量结算，供应商批量结算
 * <AUTHOR>
 */

@Component
@Slf4j
public class SvcBatchFeignFallbackFactory implements FallbackFactory<SvcBatchFeignRemote> {

    @Override
    public SvcBatchFeignRemote create(Throwable throwable) {
        SvcBatchFeignRemoteHystrix svcBatchFeignRemoteHystrix = new SvcBatchFeignRemoteHystrix();
        svcBatchFeignRemoteHystrix.setHystrixEx(throwable);
        return svcBatchFeignRemoteHystrix;
    }
}
