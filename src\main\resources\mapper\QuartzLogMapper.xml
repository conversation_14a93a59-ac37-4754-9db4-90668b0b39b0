<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qm.ep.quartz.mapper.QuartzLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <id column="id" property="id"/>
        <result column="JobName" property="JobName"/>
        <result column="JobGroup" property="JobGroup"/>
        <result column="JobStartTime" property="JobStartTime"/>
        <result column="JobEndTime" property="JobEndTime"/>
        <result column="JobRunningTime" property="JobRunningTime"/>
        <result column="JobStatus" property="JobStatus"/>
        <result column="JobStatusMes" property="JobStatusMes"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    id, JobName, JobGroup, JobStartTime, JobEndTime, JobRunningTime, JobStatus, JobStatusMes
    </sql>

    <!-- 公共查询 -->
    <sql id="QuerySQL">
        select
            a.JobName,
            a.JobGroup,
            a.JobStartTime,
            a.JobEndTime,
            a.JobRunningTime,
            a.JobStatus,
            a.JobStatusMes,
            a.id
        from task_quartz_log a
    </sql>

    <!-- 复写MP自带函数 -->
    <select id="selectByIdNew" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>
        where id = #{id}
    </select>
    <select id="selectBatchIdsNew" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>
        <if test="coll != null and !coll.isEmpty">
            <where>
                id in (<foreach collection="coll" item="item" separator=",">#{item}</foreach>)
            </where>
        </if>
    </select>
    <select id="selectByMapNew" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>
        <if test="cm != null and !cm.isEmpty">
            <where>
                <foreach collection="cm" index="k" item="v" separator="AND">
                    <choose>
                        <when test="v == null">${k} IS NULL</when>
                        <otherwise>${k} = #{v}</otherwise>
                    </choose>
                </foreach>
            </where>
        </if>
    </select>
    <select id="selectOne" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectCount" resultType="java.lang.Long">
        select count(1) from (<include refid="QuerySQL"/>${ew.customSqlSegment} ) countTable
    </select>
    <select id="selectList" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMaps" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectObjs" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectPage" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectMapsPage" resultType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        <include refid="QuerySQL"/>${ew.customSqlSegment}
    </select>
    <select id="selectRepairItemMaxJobDtstamp" resultType="java.lang.String">
        select max(JobStartTime) maxdtstamp
        from task_quartz_log
        where JobStatus = '200'
        and JobName like 'com.qm.ep.quartz.service.SvcRepairItemTimeIssuedService:repairItemTimeIssuedForDms%'
    </select>
    <select id="getLastTime" resultType="string">
        SELECT
            JobStartTime
        FROM
            `task_quartz_log`
        WHERE
            JobName LIKE '%synchdlrService%'
            AND JobGroup = 'com.qm.ep.quartz.service.SynchronousdealerService'
            AND JobStatus = 200
        ORDER BY
            Dtstamp DESC
            LIMIT 0,1
    </select>
    <select id="getCILastTime" resultType="string">
        SELECT
            JobStartTime
        FROM
            `task_quartz_log`
        WHERE
            JobName LIKE '%insertClmCheck%'
          AND JobGroup = 'com.qm.ep.quartz.service.ClmBillAutoReviewService'
          AND JobStatus = 200
        ORDER BY
            Dtstamp DESC
            LIMIT 0,1
    </select>
    <select id="getVehicleLastTime" resultType="string">
        SELECT
            JobStartTime
        FROM
            `task_quartz_log`
        WHERE
            JobName LIKE '%autVehicleService%'
            AND JobGroup = 'com.qm.ep.quartz.service.JobCustomService'
            AND JobStatus = 200
        <if test='type == "1"'>
            and JobName LIKE '%autVehicleService()%'
        </if>
        <if test='type == "2"'>
            and JobName LIKE '%autVehicleService2%'
        </if>
        <if test='type == "3"'>
            and JobName LIKE '%autVehicleService3%'
        </if>
        ORDER BY
            Dtstamp DESC
            LIMIT 0,1
    </select>
    <select id="getMdac008d1Update" resultType="string">
        SELECT
        JobStartTime
        FROM
        `task_quartz_log`
        WHERE
        JobName LIKE '%saveMdac008d1%'
        AND JobGroup = 'com.qm.ep.quartz.service.JobCustomService'
        AND JobStatus = 200
        ORDER BY
        Dtstamp DESC
        LIMIT 0,1
    </select>
    <select id="selectMaxJobDtstamp" resultType="java.lang.String">
        select max(JobStartTime) maxdtstamp
        from task_quartz_log
        where JobStatus = '200'
          and JobName like #{jobName}
    </select>
</mapper>
