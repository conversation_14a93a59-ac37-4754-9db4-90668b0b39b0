package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */

@Component
@Slf4j
public class SptCheckDataFallbackFactory implements FallbackFactory<SptCheckDataRemote> {

    @Override
    public SptCheckDataRemote create(Throwable throwable) {
        SptCheckDataRemoteHystrix priceremotehystrix = new SptCheckDataRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
