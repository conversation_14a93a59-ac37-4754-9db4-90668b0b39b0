package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SvcMntnFeignRemoteHystrix extends QmRemoteHystrix<SvcMntnFeignRemote> implements SvcMntnFeignRemote {

    @Override
    public JsonResultVo<Object> mntnAudit(String tenantId, Map<String, String> map) {
        return getResult();
    }

    @Override
    public JsonResultVo dealMntnRecordData(String tenantId){
        return getResult();
    }
    @Override
    public JsonResultVo<Object> repairMntnRecord(String tenantId) {
        return getResult();
    }

}
