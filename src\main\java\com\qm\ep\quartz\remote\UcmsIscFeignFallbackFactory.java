package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class UcmsIscFeignFallbackFactory implements FallbackFactory<UcmsIscFeignRemote> {

    @Override
    public UcmsIscFeignRemote create(Throwable throwable) {
        UcmsIscFeignRemoteHystrix feignRemoteHystrix = new UcmsIscFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
