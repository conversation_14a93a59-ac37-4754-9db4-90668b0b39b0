package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.UcmsRetFeignRemote;
import com.qm.ep.quartz.service.UcmsRetService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UcmsRetServiceImpl implements UcmsRetService {
    @Autowired
    private UcmsRetFeignRemote ucmsRetFeignRemote;

    /**
     * 定时任务发票验真
     * add by zhanghm
     */
    @Override
    public JsonResultVo invoiceVerify() {
        return ucmsRetFeignRemote.invoiceVerify("15");
    }

    /**
     * 自动申请入账
     */
    @Override
    public JsonResultVo applyAccountTask()
    {
        return ucmsRetFeignRemote.applyAccountTask("15","6000");
    }

}
