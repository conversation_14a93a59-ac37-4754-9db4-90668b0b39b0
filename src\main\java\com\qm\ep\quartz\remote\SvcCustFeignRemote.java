package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 车辆用途定时任务
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "sc-service-custom", fallbackFactory = SvcCustFeignFallbackFactory.class)
public interface SvcCustFeignRemote {

    /**
     *投诉单定时生成技术服务申请单
     */
    @PostMapping("/vehicleClass/vehicleClassTimingTask")
    JsonResultVo<Object> vehicleClassTimingTask(@RequestHeader("tenantId") String tenantId);


}
