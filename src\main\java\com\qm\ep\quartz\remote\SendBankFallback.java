package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SendBankFallback implements FallbackFactory<SendBankRemote> {
    @Override
    public SendBankRemote create(Throwable throwable) {
        SendBankRemoteHystrix sendBankRemoteHystrix = new SendBankRemoteHystrix();
        sendBankRemoteHystrix.setHystrixEx(throwable);
        return sendBankRemoteHystrix;
    }
}
