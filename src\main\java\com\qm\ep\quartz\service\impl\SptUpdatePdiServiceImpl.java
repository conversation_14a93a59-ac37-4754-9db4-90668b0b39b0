package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SalSpcApplyRemote;
import com.qm.ep.quartz.remote.SptUpdatePdiRemote;
import com.qm.ep.quartz.service.SptUpdatePdiService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 更新PDI检查标识(做成定时任务)
 *
 * @author: zhangyanpeng
 * @time: 2021/5/6
 */
@Service
public class SptUpdatePdiServiceImpl implements SptUpdatePdiService {

    @Autowired
    SptUpdatePdiRemote sptUpdatePdiRemote;
    @Autowired
    private SalSpcApplyRemote  salSpcApplyRemote;

    /**
     * 更新PDI检查标识(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @Override
    public JsonResultVo<Boolean> updatePDICheck() {
        return sptUpdatePdiRemote.updatePDICheck("15", "6000");
    }

    @Override
    public JsonResultVo<Boolean> updateMdac100d2() {
        return salSpcApplyRemote.updateMdac100d2("15", "6000");
    }

    @Override
    public JsonResultVo<Boolean> updateStatusBC(){  return sptUpdatePdiRemote.updateStatusBC("15");}
}
