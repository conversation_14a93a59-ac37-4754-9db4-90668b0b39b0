<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.qm.ep.quartz.mapper.TaskDao">


    <select id="get" resultType="com.qm.ep.quartz.domain.vo.TaskVO">
		select `id`,`cron_expression`,`method_name`,`is_concurrent`,`is_parallel`,`description`,`update_by`,`bean_class`,`create_date`,`job_status`,`job_group`,`update_date`,`create_by`,`spring_bean`,`job_name`,`create_by_name`,`update_by_name` from task_quartz where id = #{value}
	</select>

    <select id="list" resultType="com.qm.ep.quartz.domain.vo.TaskVO">
		select a.`id`,a.`cron_expression`,a.`method_name`,a.`is_concurrent`,a.`is_parallel`,a.`description`,a.`bean_class`,a.`create_date`,a.`job_status`,a.`job_group`,a.`update_date`,b.V_REAL_NAME `create_by`,a.`spring_bean`,`job_name`
		from task_quartz a

		order by job_status desc
        limit ${page},${limits};
	</select>

    <select id="maplist" resultType="com.qm.ep.quartz.domain.vo.TaskVO">
        select
        `id`,`cron_expression`,`method_name`,`is_concurrent`,`is_parallel`,`description`,`update_by`,`bean_class`,`create_date`,`job_status`,`job_group`,`update_date`,`create_by`,`spring_bean`,`job_name`
        from task_quartz
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="cronExpression != null and cronExpression != ''">and cron_expression = #{cronExpression}</if>
            <if test="methodName != null and methodName != ''">and method_name = #{methodName}</if>
            <if test="isConcurrent != null and isConcurrent != ''">and is_concurrent = #{isConcurrent}</if>
            <if test="isConcurrent != null and isConcurrent != ''">and is_parallel = #{isParallel}</if>
            <if test="description != null and description != ''">and description = #{description}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="beanClass != null and beanClass != ''">and bean_class = #{beanClass}</if>
            <if test="createDate != null and createDate != ''">and create_date = #{createDate}</if>
            <if test="jobStatus != null and jobStatus != ''">and job_status = #{jobStatus}</if>
            <if test="jobGroup != null and jobGroup != ''">and job_group = #{jobGroup}</if>
            <if test="jobPara != null and jobPara != ''">and job_para = #{jobPara}</if>
            <if test="updateDate != null and updateDate != ''">and update_date = #{updateDate}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createByName != null and createByName != ''">and create_by_name = #{createByName}</if>
            <if test="updateByName != null and updateByName != ''">and update_by_name = #{updateByName}</if>
            <if test="springBean != null and springBean != ''">and spring_bean = #{springBean}</if>
            <if test="jobName != null and jobName != ''">and job_name = #{jobName}</if>
        </where>
        <choose>
            <when test="sort != null and sort.trim() != ''">
                order by ${sort} ${order}
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="count" resultType="int">
        select count(*) from task_quartz
        <where>
            <if test="id != null and id != ''">and id = #{id}</if>
            <if test="cronExpression != null and cronExpression != ''">and cron_expression = #{cronExpression}</if>
            <if test="methodName != null and methodName != ''">and method_name = #{methodName}</if>
            <if test="isConcurrent != null and isConcurrent != ''">and is_concurrent = #{isConcurrent}</if>
            <if test="isConcurrent != null and isConcurrent != ''">and is_parallel = #{isParallel}</if>
            <if test="description != null and description != ''">and description = #{description}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="beanClass != null and beanClass != ''">and bean_class = #{beanClass}</if>
            <if test="createDate != null and createDate != ''">and create_date = #{createDate}</if>
            <if test="jobStatus != null and jobStatus != ''">and job_status = #{jobStatus}</if>
            <if test="jobGroup != null and jobGroup != ''">and job_group = #{jobGroup}</if>
            <if test="jobPara != null and jobPara != ''">and job_para = #{jobPara}</if>
            <if test="updateDate != null and updateDate != ''">and update_date = #{updateDate}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="createByName != null and createByName != ''">and create_by_name = #{createByName}</if>
            <if test="updateByName != null and updateByName != ''">and update_by_name = #{updateByName}</if>
            <if test="springBean != null and springBean != ''">and spring_bean = #{springBean}</if>
            <if test="jobName != null and jobName != ''">and job_name = #{jobName}</if>
        </where>
    </select>

    <insert id="inn" parameterType="com.qm.ep.quartz.domain.vo.TaskVO" useGeneratedKeys="true">
		insert into task_quartz
		(
			`cron_expression`, 
			`method_name`, 
			`is_concurrent`,
			`is_parallel`,
			`description`, 
			`update_by`, 
			`bean_class`, 
			`create_date`, 
			`job_status`, 
			`job_group`, 
			`update_date`, 
			`create_by`,
			`create_by_name`,
			`update_by_name`,
			`spring_bean`, 
			`job_name`
		)
		values
		(
			#{cronExpression}, 
			#{methodName}, 
			1,
			#{description}, 
			#{updateBy}, 
			#{beanClass}, 
			now(),
			#{jobStatus}, 
			#{jobGroup},
            #{jobPara},
        now(),
			#{createBy},
			#{createByName},
			#{updateByName},
			#{springBean}, 
			#{jobName}
		)
	</insert>

    <update id="update" parameterType="com.qm.ep.quartz.domain.vo.TaskVO">
        update task_quartz
        <set>
            update_date = now(),
            <if test="cronExpression != null">`cron_expression` = #{cronExpression},</if>
            <if test="methodName != null">`method_name` = #{methodName},</if>
            <if test="isConcurrent != null">`is_concurrent` = #{isConcurrent},</if>
            <if test="isConcurrent != null">`is_parallel` = #{isParallel},</if>
            <if test="description != null">`description` = #{description},</if>
            <if test="updateBy != null">`update_by` = #{updateBy},</if>
            <if test="beanClass != null">`bean_class` = #{beanClass},</if>
            <if test="createDate != null">`create_date` = #{createDate},</if>
            <if test="jobStatus != null">`job_status` = #{jobStatus},</if>
            <if test="jobGroup != null">`job_group` = #{jobGroup},</if>
            <if test="jobPara != null">`job_para` = #{jobPara},</if>
            --
            <if test="updateDate != null">`update_date` = #{updateDate},</if>
            <if test="createBy != null">`create_by` = #{createBy},</if>
            <if test="createByName != null">`create_by_name` = #{createByName},</if>
            <if test="updateByName != null">`update_by_name` = #{updateByName},</if>
            <if test="springBean != null">`spring_bean` = #{springBean},</if>
            <if test="jobName != null">`job_name` = #{jobName},</if>
            <if test="warningFlag != null">`warning_flag` = #{warningFlag}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="remove">
		delete from task_quartz where id = #{value}
	</delete>

    <delete id="batchRemove">
        delete from task_quartz where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="saveJobLog" parameterType="com.qm.ep.quartz.domain.bean.QuartzLogDO" useGeneratedKeys="true"
            keyProperty="id">
		insert task_quartz_log
		(
			`JobName`,
 			`JobStartTime`

		)
		values
		(
			#{JobName},
			#{JobStartTime}
		)
	</insert>

    <update id="updateJobLog" parameterType="com.qm.ep.quartz.domain.bean.QuartzLogDO">
        update task_quartz_log
        <set>
            <if test="JobEndTime != null">`JobEndTime` = #{JobEndTime},</if>
            <if test="JobRunningTime != null">`JobRunningTime` = #{JobRunningTime},</if>
            <if test="JobStatus != null">`JobStatus` = #{JobStatus},</if>
            <if test="JobStatusMes != null">`JobStatusMes` = #{JobStatusMes}</if>
        </set>
        where id = #{id}
    </update>

    <select id="remoteGroupList" resultType="java.lang.String">
		select distinct a.`job_group`
		from sys_task a
		order by a.`job_group`
	</select>

    <select id="remoteTaskListByGroup" parameterType="java.lang.String" resultType="java.lang.String">
		select distinct a.`job_name`
		from sys_task a
		WHERE a.`job_group` = #{value}
		order by a.`job_name`
	</select>

</mapper>