package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendDealerToDCEPRemote;
import com.qm.ep.quartz.service.Salr052DailySettleService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class Salr052DailySettleImpl implements Salr052DailySettleService {

    @Autowired
    private SendDealerToDCEPRemote SendDealerToDCEPRemote;

    @Override
    public JsonResultVo salr052DailySettle() {
        return SendDealerToDCEPRemote.salr052DailySettle("15","6000");
    }


}
