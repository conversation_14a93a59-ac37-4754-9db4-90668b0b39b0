package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;

@Repository
@FeignClient(name = "common-wf", fallbackFactory = WorkFlowLogFallbackFactory.class)
public interface WorkFlowLogRemote {

    @PostMapping("/workFlow/del")
    JsonResultVo<Object> del();

}
