package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.saveYjvoidService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class saveYjvoidImpl implements saveYjvoidService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public JsonResultVo saveYjvoid() {
        return financeTransferRemote.saveYjvoid("15", "6000");
    }
}
