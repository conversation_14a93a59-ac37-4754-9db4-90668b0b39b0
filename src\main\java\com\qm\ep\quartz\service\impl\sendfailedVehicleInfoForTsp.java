package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.sendfailedVehicleInfoForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendfailedVehicleInfoForTsp implements sendfailedVehicleInfoForTspService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public JsonResultVo sendfailedVehicleInfoForTsp() {
        return financeTransferRemote.sendfailedVehicleInfoForTsp("15", "6000");
    }
}
