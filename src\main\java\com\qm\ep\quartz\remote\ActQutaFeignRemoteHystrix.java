package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ClassName:ActQutaFeignRemoteHystrix
 * package:com.qm.ep.quartz.remote
 * Description:
 *
 * @Date:2021/6/15 13:29
 * @Author:sunyu
 */
@Component
@Slf4j
@Data
public class ActQutaFeignRemoteHystrix extends QmRemoteHystrix<ActQuotaFeignRemote> implements ActQuotaFeignRemote {

    @Override
    public JsonResultVo<Object> actQuota(String tenantId, String year, String month) {

        return getResult();
    }
}

