package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "sc-service-custom", fallbackFactory = CustomFallbackFactory.class)
public interface CustomRemote {

    /**
     * 定时任务 - 更新服务车辆档案—特殊车辆标识
     */
    @PostMapping("/autGenerateCustom/autVehicle")
    JsonResultVo autVehicle(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    /**
     * 定时任务 - 更新服务车辆档案—特殊车辆标识
     */
    @PostMapping("/autGenerateCustom/autVehicle2")
    JsonResultVo autVehicle2(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    /**
     * 定时任务 - 更新服务车辆档案—特殊车辆标识
     */
    @PostMapping("/autGenerateCustom/autVehicle3")
    JsonResultVo autVehicle3(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    /**
     * 定时任务 - 共享库向私有库同步mdac008d1
     */
    @PostMapping("/autGenerateCustom/SvcMdac008d1")
    JsonResultVo SvcMdac008d1(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    /**
     * 定时任务 - 更新重点客户车辆档案保有经销商
     */
    @PostMapping("/keyCustomer/synchronousDealer")
    JsonResultVo synchronousDealer(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    @PostMapping("/keyCustomer/synchronousDealer")
    JsonResultVo updatedlr(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> time);

    /**
     * 定时任务 - 实销上报接口
     */
    @PostMapping("/interface/sendSaleReportToDCEP")
    JsonResultVo sendSaleReportToDCEP(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 实销退车接口
     */
    @PostMapping("/interface/sendBackCarToDCEP")
    JsonResultVo sendBackCarToDCEP(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 车辆过户接口
     */
    @PostMapping("/interface/sendVehicleTransferForTSP")
    JsonResultVo sendVehicleTransferForTSP(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 用户信息修改接口
     */
    @PostMapping("/interface/sendCustInfoUpdateForTSP")
    JsonResultVo sendCustInfoUpdateForTSP(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - EV信息上报
     */
    @PostMapping("/interface/sendEVForTsp")
    JsonResultVo sendEVForTsp(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 服务重点客户下发给dms
     */
    @PostMapping("/keyCustomer/selectKeyCustomerIssuedForDms")
    JsonResultVo keyCustomerIssuedForDms(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);

    /**
     * 定时任务 - 服务车辆变更下发给dms
     */
    @PostMapping("/vehicleInfoChangeLog/selectVehicleChangeIssuedForDms")
    JsonResultVo vehicleChangeIssuedForDms(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);

    /**
     * 定时任务 - 大客户跟进计划发送钉钉消息
     */
    @PostMapping("/mdac311/sendDingMessage")
    JsonResultVo sendDingMessage2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    /**
     * 定时任务 - 服务车辆变更下发给dms
     */
    @PostMapping("/interface/sendSMRZToDMS")
    JsonResultVo sendSMRZToDMS(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 直销区实名认证信息下发给dms
     */
    @PostMapping("/interface/sendZXQSMRZToDMS")
    JsonResultVo sendZXQSMRZToDMS(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 补传
     */
    @PostMapping("/mdac301c/sendMqToSCRMForOnce")
    JsonResultVo sendMqToSCRMForOnce(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @PostMapping("/institution/dealsysc060list")
    JsonResultVo<Object> dealsysc060list(@RequestHeader("companyId") String companyId,
                                         @RequestHeader("custGroupId") String custGroupId,
                                         @RequestHeader("tenantId") String tenantId,
                                         @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 补传
     */
    @PostMapping("/mdac302/sendMqToSCRMForOnce")
    JsonResultVo send302SCRMForOnce(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给TSP下发tsp实销退车(非活体接口)
     */
    @PostMapping("/interface/sendBackCarForTsp")
    JsonResultVo sendBackCarForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给TSP传输车辆过户接口(非活体)
     */
    @PostMapping("/interface/sendVehicleTransferForTSP2")
    JsonResultVo sendVehicleTransferForTSP2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给TSP传输用户信息修改(非活体接口)
     */
    @PostMapping("/interface/sendCustInfoUpdateForTSP2")
    JsonResultVo sendCustInfoUpdateForTSP2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发tsp零售接口相关信息
     */
    @PostMapping("/interface/getnewSaleReportForTsp")
    JsonResultVo<String> getnewSaleReportForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发dcep零售接口相关信息
     */
    @PostMapping("/interface/getnewSaleReportForDCEP")
    JsonResultVo<String> getnewSaleReportForDCEP(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发tsp过户接口相关信息
     */
    @PostMapping("/interface/sendnewVehicleTransferForTSP")
    JsonResultVo<String> sendnewVehicleTransferForTSP(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发dcep过户接口相关信息
     */
    @PostMapping("/interface/sendnewVehicleTransferForDcep")
    JsonResultVo<String> sendnewVehicleTransferForDcep(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发失败的tsp零售接口相关信息
     */
    @PostMapping("/interface/getnewfailedSaleReportForTsp")
    JsonResultVo<String> getnewfailedSaleReportForTsp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 下发失败的tsp过户接口相关信息
     */
    @PostMapping("/interface/sendnewfailedVehicleTransferForTSP")
    JsonResultVo<String> sendnewfailedVehicleTransferForTSP(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 同步客户车辆档案到新能源
     */
    @PostMapping("/custVehiToXny/sendCustomerToXny")
    JsonResultVo sendCustomerToXny(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务 - 给dms下发车辆用途与销售车型对应关系
     */
    @PostMapping("/vehicle/svcVehicleClassToSaleType")
    JsonResultVo svcVehicleClassToSaleType(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);
    
    @PostMapping("/custVehiToXny/sendRetailToV2")
    JsonResultVo sendCustomerToV2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    @PostMapping("/mdac302/getSMRGBC")
    JsonResultVo<Boolean> getSMRGBC(@RequestHeader("tenantId") String tenantId);

}
