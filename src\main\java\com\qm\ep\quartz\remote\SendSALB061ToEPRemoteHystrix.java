package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
public class SendSALB061ToEPRemoteHystrix extends QmRemoteHystrix<SendSALB061ToEPRemote> implements SendSALB061ToEPRemote {

    @Override
    public JsonResultVo financeForSal() {
        return getResult();
    }
}
