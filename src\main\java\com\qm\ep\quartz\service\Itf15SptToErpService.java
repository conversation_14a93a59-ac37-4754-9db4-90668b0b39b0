package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * Spt-Erp数据接口
 *
 * @author: jianghong
 * @time: 2021/4/12 18:02
 */
public interface Itf15SptToErpService {
    /**
     * 定时任务 - 001 SptToErp：传输下线入库数据
     */
    JsonResultVo<Object> transferDataOferp001(String loginKey);

    /**
     * 定时任务 - 002 SptToErp：传输下线入库数据
     */
    JsonResultVo<Object> transferDataOferp002(String loginKey);

    /**
     * 定时任务 - 003 SptToVlms：把下线入库数据插到ep中间表
     */
    JsonResultVo<Object> insertMiddleTableOfErp001(String loginKey);

    /**
     * 定时任务 - 004 SptToVlms：把出库数据插到ep中间表
     */
    JsonResultVo<Object> insertMiddleTableOfErp002(String loginKey);

    /**
     * 定时任务 - 005 SptToVlms：把下线入库数据从ep中间表插入epr中间表
     */
    JsonResultVo<Object> transferToErp001(String loginKey);

    /**
     * 定时任务 - 006 SptToVlms：把出库数据从ep中间表插入epr中间表
     */
    JsonResultVo<Object> transferToErp002(String loginKey);

}
