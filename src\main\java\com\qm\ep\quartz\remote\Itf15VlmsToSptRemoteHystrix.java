package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15VlmsToSptRemoteHystrix extends QmRemoteHystrix<Itf15VlmsToSptRemote> implements Itf15VlmsToSptRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - VlmsToSpt：司机信息 001
     */
    @Override
    public JsonResultVo<Object> transferDataOfsjxx(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfsjxx".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：运输车信息 002
     */
    @Override
    public JsonResultVo<Object> transferDataOfyscxx(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfyscxx".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003
     */
    @Override
    public JsonResultVo<Object> transferDataOfzpyss(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfzpyss".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003 (3\4状态补传)
     */
    @Override
    public JsonResultVo<Object> transferDataOfzpyss2(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfzpyss2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：取消指派运输商 004
     */
    @Override
    public JsonResultVo<Object> transferDataOfqxzpyss(String companyId,
                                                      String custGroupId,
                                                      String tenantId,
                                                      String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfqxzpyss".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：变更运输商 005
     */
    @Override
    public JsonResultVo<Object> transferDataOfbgyss(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfbgyss".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：打印提车单 006
     */
    @Override
    public JsonResultVo<Object> transferDataOfdytcd(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfdytcd".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：车辆位置信息 007
     */
    @Override
    public JsonResultVo<Object> transferDataOfgpszt(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfgpszt".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - VlmsToSpt：在途时间点 008
     */
    @Override
    public JsonResultVo<Object> transferDataOfsjztsj(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "VlmsToSpt:transferDataOfsjztsj".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
