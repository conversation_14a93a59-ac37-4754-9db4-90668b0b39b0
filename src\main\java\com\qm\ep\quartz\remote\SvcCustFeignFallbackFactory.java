package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 车辆用途定时任务
 * <AUTHOR>
 */

@Component
@Slf4j
public class SvcCustFeignFallbackFactory implements FallbackFactory<SvcCustFeignRemote> {

    @Override
    public SvcCustFeignRemote create(Throwable throwable) {
        SvcCustFeignRemoteHystrix svcCustFeignRemoteHystrix = new SvcCustFeignRemoteHystrix();
        svcCustFeignRemoteHystrix.setHystrixEx(throwable);
        return svcCustFeignRemoteHystrix;
    }
}
