package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FeignCommonMQRemote;
import com.qm.ep.quartz.service.SendMqService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 * CreateTime: 2024/11/7
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SendMqServiceImpl implements SendMqService {
    @Autowired
    private FeignCommonMQRemote feignCommonMQRemote;

    @Override
    public JsonResultVo sendMQRetryTask() {
        log.info("SendMqService.sendMQRetryTask start");
        JsonResultVo vo = feignCommonMQRemote.sendMQRetryTask();
        log.info("SendMqService.sendMQRetryTask end");

        return vo;
    }
}
