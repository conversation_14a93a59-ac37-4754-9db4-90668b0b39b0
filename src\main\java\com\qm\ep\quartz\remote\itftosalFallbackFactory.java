package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class itftosalFallbackFactory implements FallbackFactory<itftosalRemote> {

    @Override
    public itftosalRemote create(Throwable throwable) {
        itftosalRemoteHystrix itftosalRemoteHystrix = new itftosalRemoteHystrix();
        itftosalRemoteHystrix.setHystrixEx(throwable);
        return itftosalRemoteHystrix;
    }
}
