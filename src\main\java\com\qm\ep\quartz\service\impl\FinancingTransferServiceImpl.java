package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinancingTransferFeignRemote;
import com.qm.ep.quartz.service.FinancingTransferService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FinancingTransferServiceImpl implements FinancingTransferService {
    @Autowired
    private FinancingTransferFeignRemote financingTransferFeignRemote;

    /**
     * 定时任务融资转账
     * add by zjc
     */
    @Override
    public JsonResultVo financingTransfer() {
        return financingTransferFeignRemote.financingTransfer("15");
    }

    /**
     * 定时任务融资逆转账
     * add by zjc
     */
    @Override
    public JsonResultVo financingTransferF() {
        return financingTransferFeignRemote.financingTransferF("15");
    }

    /**
     * 定时任务返利匹配
     * add by zjc
     */
    @Override
    public JsonResultVo marry() {
        return financingTransferFeignRemote.marry("15");
    }

    /**
    * @Description: 百望云上传数据
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2021/11/26
    */
    @Override
    public JsonResultVo<Object> uploadBaiwangInvoice() {
        return financingTransferFeignRemote.uploadBaiwangInvoice("15");
    }

    /**
    * @Description: 百望云发票预览
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2021/11/26
    */
    @Override
    public JsonResultVo<Object> preBaiInvoice() {
        return financingTransferFeignRemote.preBaiInvoice("15");
    }

}
