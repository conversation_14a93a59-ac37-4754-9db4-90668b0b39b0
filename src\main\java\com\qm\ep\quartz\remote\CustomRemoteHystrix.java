package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */
@Component
@Slf4j
public class CustomRemoteHystrix extends QmRemoteHystrix<CustomRemote> implements CustomRemote {
    @Override
    public JsonResultVo<Object> autVehicle(String tenantId, Map<String, String> time) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autVehicle2(String tenantId, Map<String, String> time) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autVehicle3(String tenantId, Map<String, String> time) {
        return getResult();
    }

    @Override
    public JsonResultVo SvcMdac008d1(String tenantId, Map<String, String> time) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> synchronousDealer(String tenantId, Map<String, String> time) {
        return getResult();
    }

    @Override
    public JsonResultVo updatedlr(String tenantId, Map<String, String> time) {
        return getResult();
    }

    // 实销上报接口
    @Override
    public JsonResultVo sendSaleReportToDCEP(String tenantId) {
        return getResult();
    }

    // 实销上报接口
    @Override
    public JsonResultVo sendBackCarToDCEP(String tenantId) {
        return getResult();
    }

    // 车辆过户接口
    @Override
    public JsonResultVo sendVehicleTransferForTSP(String tenantId) {
        return getResult();
    }

    // 用户信息变更接口
    @Override
    public JsonResultVo sendCustInfoUpdateForTSP(String tenantId) {
        return getResult();
    }

    // EV信息上报
    @Override
    public JsonResultVo sendEVForTsp(String tenantId) {
        return getResult();
    }

    //服务重点客户下发给dms
    @Override
    public JsonResultVo keyCustomerIssuedForDms(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }

    //服务车辆变更下发给dms
    @Override
    public JsonResultVo vehicleChangeIssuedForDms(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendDingMessage2(String tenantId, String companyID) {
        return getResult();
    }

    @Override
    public JsonResultVo sendSMRZToDMS(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendZXQSMRZToDMS(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendMqToSCRMForOnce(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo send302SCRMForOnce(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendBackCarForTsp(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendVehicleTransferForTSP2(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendCustInfoUpdateForTSP2(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> getnewSaleReportForTsp(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> getnewSaleReportForDCEP(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> sendnewVehicleTransferForTSP(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> sendnewVehicleTransferForDcep(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> getnewfailedSaleReportForTsp(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<String> sendnewfailedVehicleTransferForTSP(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendCustomerToXny(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo svcVehicleClassToSaleType(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }
    
    
    @Override
    public JsonResultVo sendCustomerToV2(String tenantId, String companyId) {
        return getResult();
    }

    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @Override
    public JsonResultVo<Object> dealsysc060list(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String errorMsg = "sysc060、sysc060d1、sysc060d2同步dealsysc060list".concat(" 接口服务维护中请耐心等待.........");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


    @Override
    public  JsonResultVo<Boolean> getSMRGBC(String tenantId){
        return getResult();
    }
}
