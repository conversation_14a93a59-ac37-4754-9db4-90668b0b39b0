package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 形成销售返利
 *
 * <AUTHOR>
 * @date 2021/6/7 9:55
 */
@Component
@Slf4j
@Data
public class AutoRebateRemoteHystrix extends QmRemoteHystrix<AutoRebateRemote> implements AutoRebateRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 形成销售返利
     *
     * @param tenantId
     * @return
     */
    @Override
    public JsonResultVo autoRebate(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.autoRebateRemoteHystrix.autoRebate");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> setJDFinishState(String tenantId) {
        return getResult();
    }
}
