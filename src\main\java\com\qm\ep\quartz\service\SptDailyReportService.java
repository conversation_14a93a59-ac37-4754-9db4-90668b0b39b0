package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * 储运日报(生成公司库存明细等)
 *
 * @author: jianghong
 * @time: 2021/4/2 10:11
 */
public interface SptDailyReportService {
    /**
     * 定时任务 - 生成公司库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    JsonResultVo insertSalb026DDO();

    /**
     * 定时任务 - 生成在途明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    JsonResultVo insertSptb022DDO();

    /**
     * 定时任务 - 生成经销商库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    JsonResultVo insertSalb027DDO();

    /**
     * 定时任务 - 使生效的运费转存到当前表
     */
    JsonResultVo transferCurrentSptc072();

}
