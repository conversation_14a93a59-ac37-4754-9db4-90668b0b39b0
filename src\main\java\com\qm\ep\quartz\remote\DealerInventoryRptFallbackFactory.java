package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 经销商库存和提车在途日报生成
 * <AUTHOR>
 * @date 2021/7/9 10:59
 */
@Component
@Slf4j
public class DealerInventoryRptFallbackFactory implements FallbackFactory<DealerInventoryRptRemote> {
    @Override
    public DealerInventoryRptRemote create(Throwable throwable) {
        DealerInventoryRptRemoteHystrix remoteHystrix = new DealerInventoryRptRemoteHystrix();
        remoteHystrix.setHystrixEx(throwable);
        return remoteHystrix;
    }
}
