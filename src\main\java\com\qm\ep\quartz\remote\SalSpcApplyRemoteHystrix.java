package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ：2021/10/9 16:03
 */
@Component
@Slf4j
@Data
public class SalSpcApplyRemoteHystrix extends QmRemoteHystrix<SalSpcApplyRemote> implements SalSpcApplyRemote {

    @Override
    public JsonResultVo<Object> voidSpcApply(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> updateMdac100d2(String tenantId, String companyId) {
        return getResult();
    }
}
