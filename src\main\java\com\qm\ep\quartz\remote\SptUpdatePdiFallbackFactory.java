package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SptUpdatePdiFallbackFactory implements FallbackFactory<SptUpdatePdiRemote> {

    @Override
    public SptUpdatePdiRemote create(Throwable throwable) {
        SptUpdatePdiHystrix SptUpdatePdiHystrix = new SptUpdatePdiHystrix();
        SptUpdatePdiHystrix.setHystrixEx(throwable);
        return SptUpdatePdiHystrix;
    }
}
