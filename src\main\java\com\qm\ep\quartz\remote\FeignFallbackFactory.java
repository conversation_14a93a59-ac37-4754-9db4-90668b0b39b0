package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class FeignFallbackFactory implements FallbackFactory<FeignRemote> {

    @Override
    public FeignRemote create(Throwable throwable) {
        FeignRemoteHystrix feignRemoteHystrix = new FeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
