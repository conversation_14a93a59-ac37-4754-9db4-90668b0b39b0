package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SendSALB061ToEPFallback implements FallbackFactory<SendSALB061ToEPRemote> {
    @Override
    public SendSALB061ToEPRemote create(Throwable throwable) {
        SendSALB061ToEPRemoteHystrix sendSALB061ToEPRemoteHystrix = new SendSALB061ToEPRemoteHystrix();
        sendSALB061ToEPRemoteHystrix.setHystrixEx(throwable);
        return sendSALB061ToEPRemoteHystrix;
    }
}
