package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


@Repository
 @FeignClient(name = "tds-service-spa-manaprice", fallbackFactory = SpaPurFeignFallbackFactory.class)
public interface SpaPurFeignRemote {

    /**
     * 定时任务执行 采购合同终审
     */
    @PostMapping("/priceApplicform/finalJudgmeByDate")
    JsonResultVo finalJudgmeByDate(@RequestHeader("tenantId") String tenantId);


}
