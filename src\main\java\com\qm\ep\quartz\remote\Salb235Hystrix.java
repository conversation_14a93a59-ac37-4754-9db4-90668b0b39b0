package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

@Component
@Slf4j
public class Salb235Hystrix extends QmRemoteHystrix<Salb235Remote> implements Salb235Remote {

    @Override
    public JsonResultVo<Object> reTryDeductionFailedJob(String tenantId) {
        return getResult();
    }
}
