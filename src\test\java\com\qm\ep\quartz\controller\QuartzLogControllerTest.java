package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.domain.dto.QuartzLogParaDTO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;

/**
 * <p>
 * ControllerTest
 * 测试类
 * </p>
 * <AUTHOR>
 * @since 2020-12-21
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class QuartzLogControllerTest extends BaseTestController<QuartzLogController> {

    @Before
    public void beforMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(QuartzLogParaDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖do
     */
    @Test
    public void moduleDoTest() {
        EqualsVerifier.simple().forClass(QuartzLogDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }



    @Test
    public void selectList() {
        QuartzLogParaDTO dto = this.moduleEmpty(QuartzLogParaDTO.class);
        JsonResultVo resultVo;

        resultVo = this.testController.selectList(dto);
        this.assertJsonResultVo(resultVo);

        dto = this.moduleFill(QuartzLogParaDTO.class);
        resultVo = this.testController.selectList(dto);
        this.assertJsonResultVo(resultVo);
    }
}