package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ly add 20210701
 */
@Component
@Slf4j
public class FeignTempleFallbackFactory implements FallbackFactory<FeignTempleRemote> {

    @Override
    public FeignTempleRemote create(Throwable throwable) {
        FeignTempleRemoteHystrix feignTempleRemoteHystrix = new FeignTempleRemoteHystrix();
        feignTempleRemoteHystrix.setHystrixEx(throwable);
        return feignTempleRemoteHystrix;
    }
}
