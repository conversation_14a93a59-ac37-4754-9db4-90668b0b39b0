package com.qm.ep.quartz.service.impl;


import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.itftosalService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class itftosalServiceImpl implements itftosalService {

    @Autowired
    com.qm.ep.quartz.remote.itftosalRemote itftosalRemote;
    @Autowired
    CustomRemote customRemote;

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }


    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @Override
    public JsonResultVo<Object> dealsysc060list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealsysc060list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealsysc005list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealsysc005list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }


    @Override
    public JsonResultVo<Object> dealsysi051list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealsysi051list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealsysc009dlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealsysc009dlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac001clist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac001clist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac001dlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac001dlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac001plist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac001plist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac001rlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac001rlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac002list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac002list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac002dlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac002dlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac009list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac009list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac009dlist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac009dlist(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    @Override
    public JsonResultVo<Object> dealmdac600list(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itftosalRemote.dealmdac600list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -sysc060、sysc060d1、sysc060d2同步
     */
    @Override
    public JsonResultVo<Object> dealsysc060toscvclist(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return customRemote.dealsysc060list(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

}
