package com.qm.ep.quartz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.vo.QuartzStatisticsVO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
public interface QuartzMapper extends QmBaseMapper<QuartzDO> {
    List<QuartzDO> selectDistinctGroup(Map map);

    List<QuartzDO> selectDistinctName(Map map);

    List<QuartzStatisticsVO> statisticsTable(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("list") List<String> list);

    IPage<QuartzStatisticsVO> statisticsTableByQuery(IPage<QuartzStatisticsVO> page, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("list") List<String> list);
}
