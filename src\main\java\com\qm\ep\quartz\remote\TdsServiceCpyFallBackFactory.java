package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;

public class TdsServiceCpyFallBackFactory implements FallbackFactory<TdsServiceCpyRemote> {
    @Override
    public TdsServiceCpyRemote create(Throwable throwable) {
        TdsServiceCpyHystrix feignRemoteHystrix = new TdsServiceCpyHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
