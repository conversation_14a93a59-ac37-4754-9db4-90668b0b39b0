package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.TempnbykDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-spt-query", fallbackFactory = SptQueryXxrkFallbackFactory.class)
public interface SptQueryXxrkRemote {

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse1")
    public JsonResultVo<Boolean> executeInWarehouse1(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse2")
    public JsonResultVo<Boolean> executeInWarehouse2(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse3")
    public JsonResultVo<Boolean> executeInWarehouse3(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse4")
    public JsonResultVo<Boolean> executeInWarehouse4(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse5")
    public JsonResultVo<Boolean> executeInWarehouse5(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse6")
    public JsonResultVo<Boolean> executeInWarehouse6(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse7")
    public JsonResultVo<Boolean> executeInWarehouse7(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse8")
    public JsonResultVo<Boolean> executeInWarehouse8(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse9")
    public JsonResultVo<Boolean> executeInWarehouse9(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse10")
    public JsonResultVo<Boolean> executeInWarehouse10(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse11")
    public JsonResultVo<Boolean> executeInWarehouse11(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse12")
    public JsonResultVo<Boolean> executeInWarehouse12(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse13")
    public JsonResultVo<Boolean> executeInWarehouse13(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse14")
    public JsonResultVo<Boolean> executeInWarehouse14(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 下线入库1-1000(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempxxrk/executeInWarehouse15")
    public JsonResultVo<Boolean> executeInWarehouse15(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);




    /**
     * 内移库出(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempnbyk/internalMoveOutTime")
    public JsonResultVo<Boolean> internalMoveOutTime(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestBody TempnbykDTO tempnbykDTO);



    /**
     * 内移库出(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/tempnbyk/internalMoveInTime")
    public JsonResultVo<Boolean> internalMoveInTime(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestBody TempnbykDTO tempnbykDTO);

}
