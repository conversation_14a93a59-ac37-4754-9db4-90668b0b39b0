package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface WmsInterfaceService {

    // wms物料主数据下发接口
    JsonResultVo spaMasterDocumentIssued();

    // wms收货单下发接口
    JsonResultVo receiptIssuedInterface();

    // wms提货单下发接口
    JsonResultVo spaBillOfLadingIssued();

    // wms库存对比定时任务
    JsonResultVo compareInventory();
    // wms库存异动定时任务
    JsonResultVo inventoryChange();

    // OTD同步精品配件信息接口 定时任务
    JsonResultVo syncBoutiqueData();





}
