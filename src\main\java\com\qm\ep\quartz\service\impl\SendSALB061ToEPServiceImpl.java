package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendSALB061ToEPRemote;
import com.qm.ep.quartz.service.SendSALB061ToEPService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Service
public class SendSALB061ToEPServiceImpl implements SendSALB061ToEPService {
    @Autowired
    private SendSALB061ToEPRemote sendSALB061ToEPRemote;

    @Override
    public JsonResultVo financeForSal() {
        return sendSALB061ToEPRemote.financeForSal();
    }
}
