package com.qm.ep.quartz.domain.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
@Data
@TableName("task_quartz")
@Schema(title = "定时任务基本信息", description = "定时任务基本信息")
public class QuartzDO implements Serializable {

    private static final long serialVersionUID = 7048335464359170301L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @Schema(title = "id", description = "id")
    private String id;

    /**
     * cron表达式
     */
    @Schema(title = "cron表达式", description = "cron表达式")
    private String cronExpression;

    /**
     * 任务调用的方法名
     */
    @Schema(title = "任务调用的方法名", description = "任务调用的方法名")
    private String methodName;

    /**
     * 任务是否有状态
     */
    @Schema(title = "任务是否有状态", description = "任务是否有状态")
    private String isConcurrent;

    /**
     * 任务描述
     */
    @Schema(title = "任务描述", description = "任务描述")
    private String description;

    /**
     * 更新者
     */
    @Schema(title = "更新者", description = "更新者")
    @TableField("update_by")
    private String updateBy;

    /**
     * 任务执行时调用哪个类的方法 包名+类名
     */
    @Schema(title = "任务执行时调用哪个类的方法 包名+类名", description = "任务执行时调用哪个类的方法 包名+类名")
    private String beanClass;

    /**
     * 创建时间
     */
    @Schema(title = "创建时间", description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    //@Version
    @TableField(value = "create_date" ,fill = FieldFill.INSERT)
    private Date createDate;

    /**
     * 任务状态
     */
    @Schema(title = "任务状态", description = "任务状态")
    @TableField("job_status")
    private String jobStatus;

    /**
     * 任务分组
     */
    @Schema(title = "任务分组", description = "任务分组")
    @TableField("job_group")
    private String jobGroup;

    /**
     * 更新时间
     */
    @Schema(title = "更新时间", description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    //@Version
    @TableField(value = "update_date" ,fill = FieldFill.UPDATE)
    private Date updateDate;

    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    @TableField("create_by_name")
    private String createByName;

    /**
     * 创建者
     */
    @Schema(title = "修改者", description = "修改者")
    @TableField("update_by_name")
    private String updateByName;


    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    @TableField("create_by")
    private String createBy;

    /**
     * Spring bean
     */
    @Schema(title = "Spring bean", description = "Spring bean")
    private String springBean;


    /**
     * Spring bean
     */
    @Schema(title = "任务参数", description = "任务参数")
    @TableField("job_para")
    private String jobPara;
    /**
     * 任务名
     */
    @Schema(title = "任务名", description = "任务名")
    @TableField("job_name")
    private String jobName;

    /**
     * 重叠执行
     */
    @Schema(title = "重叠执行", description = "重叠执行")
    @TableField("is_parallel")
    private String isParallel;

    /**
     * 时间戳
     */
    @Version
    @Schema(title = "时间戳", description = "时间戳")
    @TableField(value = "dtstamp", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;


    /**
     * 预警标识
     */
    @Schema(title = "预警标识", description = "预警标识")
    @TableField("warning_flag")
    private String warningFlag;
}
