package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class AutoShortageStatisticsRemoteHystrix extends QmRemoteHystrix<AutoShortageStatisticsRemote> implements AutoShortageStatisticsRemote{
    @Override
    public JsonResultVo<Object> AutoShortageStatistics(String tenantId) {return getResult();}

    @Override
    public JsonResultVo<Object> autoSummarizesStatisticsData(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> generateCgddGzData(String tenantId) {
        return getResult();
    }
}
