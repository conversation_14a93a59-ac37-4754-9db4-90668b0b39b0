package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface FinancingTransferService {
    /**
     * 处理融资转账
     * zjc
     * 20210426
     */
    JsonResultVo financingTransfer();

    /**
     * 处理融资逆转账
     * zjc
     * 20210426
     */
    JsonResultVo financingTransferF();

    /**
     * 处理返利匹配
     * zjc
     * 20210529
     */
    JsonResultVo marry();

    /**
    * @Description: 百望云上传数据
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2021/11/26
    */
    JsonResultVo<Object> uploadBaiwangInvoice();

    /**
     * @Description: 百望云发票预览
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/11/26
     */
    JsonResultVo<Object> preBaiInvoice();
}
