package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.bean.Mdac315CDO;
import com.qm.ep.quartz.domain.dto.Mdac315CDListDTO;
import com.qm.ep.quartz.domain.dto.Mdac315MiddleDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
public class SendSALB061ToV2RemoteHystrix extends QmRemoteHystrix<SendSALB061ToV2Remote> implements SendSALB061ToV2Remote {

    @Override
    public JsonResultVo sendDataToV2(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo transferSalb064ToTdsv2(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendProvinceCityToDMS(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendDealerInfoToDMS(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Mdac315CDListDTO> getMdac315CAuditData(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Mdac315CDO> autoResourceAllocation(String tenantId, String companyId, Mdac315MiddleDTO tempDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo sendProductPriceToDMS(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendProductPriceToDMSXny(String tenantId, String companyId) {
        return getResult();
    }
}
