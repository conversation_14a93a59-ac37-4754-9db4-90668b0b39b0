package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class StatisticsFeignRemoteHystrix extends QmRemoteHystrix<StatisticsFeignRemote> implements StatisticsFeignRemote {

    @Override
    public JsonResultVo<Object> syncDealerBusinessData(String tenantId, Map param) {
        return getResult();
    }

}
