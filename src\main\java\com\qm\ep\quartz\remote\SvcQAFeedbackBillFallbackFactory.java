package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 质量反馈机审
 */

@Component
@Slf4j
public class SvcQAFeedbackBillFallbackFactory implements FallbackFactory<SvcQAFeedbackBillFeignRemote> {

    @Override
    public SvcQAFeedbackBillFeignRemote create(Throwable throwable) {
        SvcQAFeedbackBillRemoteHystrix svcQAFeedbackBillRemoteHystrix = new SvcQAFeedbackBillRemoteHystrix();
        svcQAFeedbackBillRemoteHystrix.setHystrixEx(throwable);
        return svcQAFeedbackBillRemoteHystrix;
    }
}
