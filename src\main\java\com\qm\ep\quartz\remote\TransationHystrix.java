package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class TransationHystrix extends QmRemoteHystrix<TransactionRemote> implements TransactionRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Object> transationVoid(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.transationVoid");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> weekRelease(String day, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.weekRelease");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> erpSaljhpcjg(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> erpSalDdjh(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> sendDDRKToERP(String addRess, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> sendFYCKToERP(String addRess, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> sendJXSRKToERP(String addRess, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> sendKHJFToERP(String addRess, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> sendRGRLToERP(String addRess, String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.common");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> syncVehicleFeature(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("MSG.quartz.common.success");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> syncDmsOrder(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendPlanCalerderToJCK(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.TransationHystrix.sendPlanCalerderToJCK");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsg(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> sendDealerInfoToOTD(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendMatchPorduct(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> weekEditRelease(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> syncDmsOrdersUploadLog(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> custOrderProcessChang(String tenantId, String companyId) {
        return getResult();
    }
}
