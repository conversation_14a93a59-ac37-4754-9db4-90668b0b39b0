package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;

public interface SvcGuaranService {
    JsonResultVo<Object> tsTimingTask1();

    JsonResultVo<Object> ffVComplaintBill();

    JsonResultVo<Object> tsTimingBill(String loginKey);

    JsonResultVo<Object> serviceSupportAutoReview();

    JsonResultVo<Object> policyMntnAutoReview(String loginKey);

    JsonResultVo<Object> vdrAutoReply(String loginKey);
}
