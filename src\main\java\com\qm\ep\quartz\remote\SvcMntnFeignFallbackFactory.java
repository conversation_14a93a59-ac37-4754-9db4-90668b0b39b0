package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SvcMntnFeignFallbackFactory implements FallbackFactory<SvcMntnFeignRemote> {

    @Override
    public SvcMntnFeignRemote create(Throwable throwable) {
        SvcMntnFeignRemoteHystrix feignRemoteHystrix = new SvcMntnFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
