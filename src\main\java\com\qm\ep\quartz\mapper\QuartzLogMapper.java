package com.qm.ep.quartz.mapper;

import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.tds.api.mp.mapper.QmBaseMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Service
public interface QuartzLogMapper extends QmBaseMapper<QuartzLogDO> {
    String selectRepairItemMaxJobDtstamp();
    String getLastTime();
    String getCILastTime();
    String getVehicleLastTime(String type);

    String getMdac008d1Update();
    /**
     * 获取job执行成功的最大job开始时间戳
     * jobName是带有 % 的
     * @return
     */
    String selectMaxJobDtstamp(String jobName);
}
