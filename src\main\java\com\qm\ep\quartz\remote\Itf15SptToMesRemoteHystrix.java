package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToMesRemoteHystrix extends QmRemoteHystrix<Itf15SptToMesRemote> implements Itf15SptToMesRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 SptToMes：传输库存数据
     */
    @Override
    public JsonResultVo<Object> transferDataOfInventory(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToMes:transferDataOfInventory".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     *  定时任务 - 002 SptToMes：蔚山1厂下线入库回传接口(恒展系统)
     */
    @Override
    public JsonResultVo<Object> transferToHzmes(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToMes:transferToHzmes".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 003 SptToMes：繁荣工厂下线入库回传接口
     */
    @Override
    public JsonResultVo<Object> transferToFrmom(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToMes:transferToFrmom".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 004 SptToMes：蔚山2工厂下线入库回传接口
     */
    @Override
    public JsonResultVo<Object> transferToWs2mom(String companyId,
                                                String custGroupId,
                                                String tenantId,
                                                String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToMes:transferToWs2mom".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
