package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 衍生金融
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "ut-service-fin", fallbackFactory = FeignFallbackFactory.class)
public interface UTFinFeignRemote {

    /**
     * 更新贴息单状态FDTEditStatesInfo
     */
    @PostMapping("/finQuartz/updateTxdState")
    JsonResultVo updateTxdState(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 同步状态fdtStateJOB
     */
    @PostMapping("/finQuartz/syncTxdStatus")
    JsonResultVo syncTxdStatus(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 自动申请入账fdtPopJOB
     */
    @PostMapping("/finQuartz/txdEnterAccount")
    JsonResultVo txdEnterAccount(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 自动下发状态fdtDowStateJOB
     */
    @PostMapping("/finQuartz/updateTxdTimestamp")
    JsonResultVo updateTxdTimestamp(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 给DMS下发金融政策fdtInsPolicyInfo
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/finQuartz/sendInsPolicyToDms")
    JsonResultVo sendInsPolicyToDms(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 给DMS下发贴息单状态fdtYYDiscountDocStatesInfo
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/finQuartz/sendTxdStateToDms")
    JsonResultVo sendTxdStateToDms(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
    @PostMapping("/fDTDiscountDoc/getInvoiceListUpdateDiscountDoc")
    JsonResultVo getInvoiceListUpdateDiscountDoc(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);
}
