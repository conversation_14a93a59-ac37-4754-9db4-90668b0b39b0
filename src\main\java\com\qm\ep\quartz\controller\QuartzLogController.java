package com.qm.ep.quartz.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.domain.dto.QuartzLogParaDTO;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.mp.pagination.QmPage;
import com.qm.tds.api.mp.wrapper.QmQueryWrapper;
import com.qm.tds.util.BootAppUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Tag(name = "<p> 前端控制器 </p>", description = "<p> 前端控制器 </p>")
@RestController
@RequestMapping("/quartzLog")
public class QuartzLogController extends BaseController {

    @Autowired
    private QuartzLogService quartzLogService;


    @Operation(summary = "", description = "[author:10234090]")
    @PostMapping("selectList")
    public JsonResultVo selectList(@RequestBody QuartzLogParaDTO quartzLogParaDTO) {
        JsonResultVo ret = new JsonResultVo();
        QmQueryWrapper<QuartzLogDO> qmQueryWrapper = new QmQueryWrapper<>();
        LambdaQueryWrapper<QuartzLogDO> lambdaWrapper = qmQueryWrapper.lambda();
        String jobGroup = quartzLogParaDTO.getJobGroup();
        String jobName = quartzLogParaDTO.getJobName();

        if (jobName != null && !"".equals(jobName)) {
            lambdaWrapper.like(QuartzLogDO::getJobName, jobName);
        }
        if (jobGroup != null && !"".equals(jobGroup)) {
            lambdaWrapper.like(QuartzLogDO::getJobGroup, jobGroup);
        }
        if (!BootAppUtil.isNullOrEmpty(quartzLogParaDTO.getStart())) {
            lambdaWrapper.gt(QuartzLogDO::getJobStartTime, quartzLogParaDTO.getStart());
        }
        if (!BootAppUtil.isNullOrEmpty(quartzLogParaDTO.getEnd())) {
            lambdaWrapper.lt(QuartzLogDO::getJobEndTime, quartzLogParaDTO.getEnd());
        }
        // 默认开始时间倒序
        lambdaWrapper.orderByDesc(QuartzLogDO::getJobStartTime);
        QmPage<QuartzLogDO> list = quartzLogService.table(qmQueryWrapper, quartzLogParaDTO);
        ret.setData(list);
        return ret;
    }
}
