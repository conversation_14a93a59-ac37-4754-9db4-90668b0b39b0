package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.TransactionRemote;
import com.qm.ep.quartz.service.SaltransationVoidService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaltransationVoidServiceImpl implements SaltransationVoidService {

    @Autowired
    private TransactionRemote transactionRemote;

    /**
     * 交易自动作废
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/4/27
     */
    @Override
    public JsonResultVo<Object> transationVoid() {
        return transactionRemote.transationVoid("15", "6000");
    }

    /**
     * 周订单周末释放
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/4/27
     */
    @Override
    public JsonResultVo<Object> weekRelease(String day) {
        return transactionRemote.weekRelease(day, "15", "6000");
    }

    /**
     * 月计划高排接口
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/21
     */
    @Override
    public JsonResultVo<Object> erpSaljhpcjg() {
        return transactionRemote.erpSaljhpcjg("15", "6000");
    }

    /**
     * 月计划订单计划接口
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> erpSalDdjh() {
        return transactionRemote.erpSalDdjh("15", "6000");
    }

    /**
     * 订单中心——订单入库信息
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> sendDDRKToERP(String addRess) {
        return transactionRemote.sendDDRKToERP(addRess, "15", "6000");
    }

    /**
     * 发运出库信息
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> sendFYCKToERP(String addRess) {
        return transactionRemote.sendFYCKToERP(addRess, "15", "6000");
    }

    /**
     * 经销商入库信息
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> sendJXSRKToERP(String addRess) {
        return transactionRemote.sendJXSRKToERP(addRess, "15", "6000");
    }

    /**
     * 客户交付信息
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> sendKHJFToERP(String addRess) {
        return transactionRemote.sendKHJFToERP(addRess, "15", "6000");
    }

    /**
     * 人工日历信息
     *
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2021/5/25
     */
    @Override
    public JsonResultVo<Object> sendRGRLToERP(String addRess) {
        return transactionRemote.sendRGRLToERP(addRess, "15", "6000");
    }

    @Override
    public JsonResultVo<Object> syncVehicleFeature(String addRess) {
        return transactionRemote.syncVehicleFeature("15", "6000");
    }

    @Override
    public JsonResultVo<Object> syncDmsOrder() {
        return transactionRemote.syncDmsOrder("15", "6000");
    }

    /**
     * @Description: 同步进出口计划日历
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/5/19
     */
    @Override
    public JsonResultVo<Object> sendPlanCalerderToJCK() {
        return transactionRemote.sendPlanCalerderToJCK("15", "6000");
    }

    /**
     * 给OTD同步经销商主数据
     *
     * @return
     */
    @Override
    public JsonResultVo<Boolean> sendDealerInfoToOTD() {
        return transactionRemote.sendDealerInfoToOTD("15", "6000");
    }

    /**
     * @Description: 定时任务-匹配排产明细数据
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    @Override
    public JsonResultVo<Object> sendMatchPorduct() {
        return transactionRemote.sendMatchPorduct("15", "6000");
    }

    /**
     * @Description: 定时任务-AB类订单明细释放
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    @Override
    public JsonResultVo<Object> weekEditRelease() {
        return transactionRemote.weekEditRelease("15", "6000");
    }
    
    /**
     * @Description: 同步DMS销售订单上传记录
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 许可
     * @Date: 2022/12/10
     */
    @Override
    public JsonResultVo<Object> syncDmsOrdersUploadLog() {
        return transactionRemote.syncDmsOrdersUploadLog("15", "6000");
    }

    /**
    * @Description: 下发客户订单进度变动信息
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/4/14
    */
    @Override
    public JsonResultVo<Object> custOrderProcessChang() {
        return transactionRemote.custOrderProcessChang("15", "6000");
    }

}
