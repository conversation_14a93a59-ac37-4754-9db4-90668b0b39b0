package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.service.SptDailyReportService;
import com.qm.ep.quartz.service.SptUpdatePdiService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: jianghong
 * @time: 2021/4/2 14:21
 */
@Tag(name = "调试定时任务使用", description = "调试定时任务使用")
@RestController
@RequestMapping("/sptDailyReport")
public class SptDailyReportController extends BaseController {
    @Autowired
    SptDailyReportService sptDailyReportService;
    @Autowired
    SptUpdatePdiService sptUpdatePdiService;

    /**
     * 定时任务 - 生成公司库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Operation(summary = "生成公司库存明细日报", description = "生成公司库存明细日报[author:10027705]")
    @PostMapping("/insertSalb026DDO")
    public JsonResultVo insertSalb026DDO() {
        return sptDailyReportService.insertSalb026DDO();
    }

    /**
     * 定时任务 - 生成在途明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Operation(summary = "生成在途明细日报", description = "生成在途明细日报[author:10027705]")
    @PostMapping("/insertSptb022DDO")
    public JsonResultVo insertSptb022DDO() {
        return sptDailyReportService.insertSptb022DDO();
    }

    /**
     * 定时任务 - 生成经销商库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Operation(summary = "生成经销商库存明细日报", description = "生成经销商库存明细日报[author:10027705]")
    @PostMapping("/insertSalb027DDO")
    public JsonResultVo insertSalb027DDO() {
        return sptDailyReportService.insertSalb027DDO();
    }

    @Operation(summary = "使生效的运费转存到当前表", description = "使生效的运费转存到当前表[author:10027705]")
    @PostMapping("/transferCurrentSptc072")
    public JsonResultVo transferCurrentSptc072() {
        return sptDailyReportService.transferCurrentSptc072();
    }

    @Operation(summary = "更新PDI检查标识", description = "更新PDI检查标识[author:10027705]")
    @PostMapping("/updatePDICheck")
    public JsonResultVo updatePDICheck() {
        return sptUpdatePdiService.updatePDICheck();
    }
}
