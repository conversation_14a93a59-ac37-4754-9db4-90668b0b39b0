package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.quartz.remote.SvcGuaranFeignRemote;
import com.qm.ep.quartz.service.SvcGuaranService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SvcGuaranServiceImpl implements SvcGuaranService {
    @Autowired
    private SvcGuaranFeignRemote feignRemote;

    @Override
    public JsonResultVo<Object> tsTimingTask1() {
        return feignRemote.tsTimingTask("15");
    }

    @Override
    public JsonResultVo<Object> ffVComplaintBill(){
        return feignRemote.ffVComplaintBill("15");
    }

    @Override
    public JsonResultVo<Object> tsTimingBill(String loginKey){
        LoginKeyDO loginKeyDO = JSONObject.parseObject(loginKey,LoginKeyDO.class);
        return feignRemote.tsTimingBill("15",loginKeyDO);
    }

    @Override
    public JsonResultVo<Object> serviceSupportAutoReview(){
        return feignRemote.serviceSupportAutoReview("15");
    }

    @Override
    public JsonResultVo<Object> policyMntnAutoReview(String loginKey) {
        LoginKeyDO loginKeyDO = JSONObject.parseObject(loginKey,LoginKeyDO.class);
        return feignRemote.policyMntnAutoReview("15",loginKeyDO);
    }

    @Override
    public JsonResultVo<Object> vdrAutoReply(String loginKey) {
        LoginKeyDO loginKeyDO = JSONObject.parseObject(loginKey,LoginKeyDO.class);
        return feignRemote.vdrAutoReply(loginKeyDO.getTenantId(),loginKeyDO);
    }
}

