package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SptDailyReportRemote;
import com.qm.ep.quartz.service.SptDailyReportService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: jianghong
 * @time: 2021/4/2 10:15
 */
@Service
public class SptDailyReportServiceImpl implements SptDailyReportService {

    @Autowired
    SptDailyReportRemote sptDailyReportRemote;

    /**
     * 定时任务 - 生成公司库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo insertSalb026DDO() {
        return sptDailyReportRemote.insertSalb026DDO("15", "6000");
    }

    /**
     * 定时任务 - 生成在途明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo insertSptb022DDO() {
        return sptDailyReportRemote.insertSptb022DDO("15", "6000");
    }

    /**
     * 定时任务 - 生成经销商库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo insertSalb027DDO() {
        return sptDailyReportRemote.insertSalb027DDO("15", "6000");
    }

    /**
     * 定时任务 - 使生效的运费转存到当前表
     */
    @Override
    public JsonResultVo transferCurrentSptc072() {
        return sptDailyReportRemote.transferCurrentSptc072("15", "6000");
    }
}
