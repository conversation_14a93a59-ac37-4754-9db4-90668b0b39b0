package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class TdsSpaRemoteFallbackFactory implements FallbackFactory<TdsSpaRemote> {

    @Override
    public TdsSpaRemote create(Throwable throwable) {
        TdsSpaRemoteHystrix priceremotehystrix = new TdsSpaRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
