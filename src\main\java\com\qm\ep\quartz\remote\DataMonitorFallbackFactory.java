package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24
 */
@Component
@Slf4j
public class DataMonitorFallbackFactory implements FallbackFactory<DataMonitorRemote> {
    @Override
    public DataMonitorRemote create(Throwable throwable) {
        DataMonitorRemoteHystrix hystrix = new DataMonitorRemoteHystrix();
        hystrix.setHystrixEx(throwable);
        return hystrix;
    }
}
