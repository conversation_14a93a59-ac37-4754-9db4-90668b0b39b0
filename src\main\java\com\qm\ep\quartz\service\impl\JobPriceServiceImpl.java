package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.PriceRemote;
import com.qm.ep.quartz.service.JobPriceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class JobPriceServiceImpl implements JobPriceService {

    @Autowired
    private PriceRemote priceRemote;

    @Override
    public JsonResultVo priceService() {
        return priceRemote.generate("15");
    }
}
