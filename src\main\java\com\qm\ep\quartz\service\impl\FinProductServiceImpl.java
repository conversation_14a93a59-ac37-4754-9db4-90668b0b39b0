package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinRemote;
import com.qm.ep.quartz.service.FinProductService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : FinanceServiceImpl
 * @Description :
 * <AUTHOR> WQS
 * @Date: 2021-03-27  10:10
 */
@Service
public class FinProductServiceImpl implements FinProductService {

    @Autowired
    FinRemote finRemote;

    /**
     * 向v2传送产品代码
     *
     * <AUTHOR>
     */
    @Override
    public JsonResultVo<List<Map<String, String>>> sendFinProduct() {
        return finRemote.itf_ep_mdac008c();
    }
    
}
