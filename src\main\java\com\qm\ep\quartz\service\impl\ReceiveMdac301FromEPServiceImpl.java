package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ReceiveMdac301FromEPRemote;
import com.qm.ep.quartz.service.ReceiveMdac301FromEPService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * V2接收EP客户信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-23
 */
@Slf4j
@Service
public class ReceiveMdac301FromEPServiceImpl implements ReceiveMdac301FromEPService {
    @Autowired
    private ReceiveMdac301FromEPRemote receiveMdac301FromEPRemote;
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 接收客户信息数据，其中包括客户档案基本信息 联系人信息 发票信息
     *
     * @return
     */
    @Override
    public JsonResultVo<Boolean> ReceiveMdac301() {
        JsonResultVo<Boolean> resultVo;
        StringBuilder sbuilder = new StringBuilder();
        resultVo = receiveMdac301FromEPRemote.receiveEPMdac301c("15");
        String message = i18nUtil.getMessage("MSG.quartz.common.success");
        if (resultVo.getCode() != 200) {
            log.info(resultVo.getMsg());
            sbuilder.append("mdac301c:" + resultVo.getMsg());
        } else {
            sbuilder.append("mdac301c:" + message);
        }
        resultVo = receiveMdac301FromEPRemote.receiveEPMdac300("15");
        if (resultVo.getCode() != 200) {
            log.info(resultVo.getMsg());
            sbuilder.append("mdac300:" + resultVo.getMsg());
        } else {
            sbuilder.append("mdac300:" + message);
        }
        resultVo = receiveMdac301FromEPRemote.receiveEPMdac301d6("15");
        if (resultVo.getCode() != 200) {
            log.info(resultVo.getMsg());
            sbuilder.append("mdac301d6:" + resultVo.getMsg());
        } else {
            sbuilder.append("mdac301d6:" + message);
        }
        resultVo.setMsg(sbuilder.toString());
        return resultVo;
    }
}
