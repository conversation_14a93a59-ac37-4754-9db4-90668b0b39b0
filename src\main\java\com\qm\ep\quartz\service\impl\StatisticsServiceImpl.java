package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.qm.ep.quartz.remote.StatisticsFeignRemote;
import com.qm.ep.quartz.service.StatisticsService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class StatisticsServiceImpl implements StatisticsService {
    @Autowired
    private StatisticsFeignRemote feignRemote;

    @Override
    public JsonResultVo<Object> tsTimingTask(String params) {
        JSONObject obj = JSONObject.parseObject(params);
        Map map = new HashMap();
        map.put("dbegin", obj.getString("dbegin"));
        map.put("dend", obj.getString("dend"));
        return feignRemote.syncDealerBusinessData("15", map);
    }

}

