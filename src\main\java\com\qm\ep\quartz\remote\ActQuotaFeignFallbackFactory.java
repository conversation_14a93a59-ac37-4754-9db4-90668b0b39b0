package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ClassName:ActQuotaFeignFallbackFactory
 * package:com.qm.ep.quartz.remote
 * Description:
 *
 * @Date:2021/6/15 13:28
 * @Author:sunyu
 */

@Component
@Slf4j
public class ActQuotaFeignFallbackFactory implements FallbackFactory<ActQuotaFeignRemote> {

    @Override
    public ActQuotaFeignRemote create(Throwable throwable) {
        ActQutaFeignRemoteHystrix feignRemoteHystrix = new ActQutaFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}