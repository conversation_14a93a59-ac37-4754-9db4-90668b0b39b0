package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.domain.dto.AutoGenerateMonthReportDTO;
import com.qm.ep.quartz.domain.dto.AutoGenerateWeekReportDTO;
import com.qm.ep.quartz.remote.GeneratePartFinanceReportRemote;
import com.qm.ep.quartz.service.GeneratePartFinanceReportService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
public class GeneratePartFinanceReportServiceImpl implements GeneratePartFinanceReportService {

    @Autowired
    private GeneratePartFinanceReportRemote generatePartFinanceReportRemote;

    @Override
    public JsonResultVo generatPartMonthReport() {
        AutoGenerateMonthReportDTO paramsDTO = new AutoGenerateMonthReportDTO();
        return generatePartFinanceReportRemote.generatPartMonthReport("15",paramsDTO);
    }

    @Override
    public JsonResultVo generatPartWeekReport() {
         AutoGenerateWeekReportDTO paramsDTO = new AutoGenerateWeekReportDTO();
        return generatePartFinanceReportRemote.generatPartWeekReport("15",paramsDTO);
    }

    @Override
    public JsonResultVo autoGenerateBeginEndDate() {
        return generatePartFinanceReportRemote.autoGenerateBeginEndDate("15");
    }

    @Override
    public JsonResultVo generateSpaPenetrationRateInfo() {
        return generatePartFinanceReportRemote.generateSpaPenetrationRateInfo("15");
    }

    @Override
    public JsonResultVo autoCal517() {
        return generatePartFinanceReportRemote.autoCal517("15");
    }
}
