package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendBackCarForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendBackCarForTspImpl implements SendBackCarForTspService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendBackCarForTsp() {
        return customRemote.sendBackCarForTsp("15", "6000");
    }


}
