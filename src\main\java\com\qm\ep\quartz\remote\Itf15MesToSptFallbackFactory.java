package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - Mes生产下线接口
 */

@Component
@Slf4j
public class Itf15MesToSptFallbackFactory implements FallbackFactory<Itf15MesToSptRemote> {

    @Override
    public Itf15MesToSptRemote create(Throwable throwable) {
        Itf15MesToSptRemoteHystrix priceremotehystrix = new Itf15MesToSptRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
