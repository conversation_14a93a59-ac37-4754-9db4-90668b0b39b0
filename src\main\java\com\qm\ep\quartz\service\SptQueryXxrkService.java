package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * 更新PDI检查标识(做成定时任务)
 *
 * @author: zhangyanpeng
 * @time: 2021/5/6
 */
public interface SptQueryXxrkService {
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse1();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse2();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse3();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse4();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse5();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse6();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse7();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse8();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse9();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse10();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse11();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse12();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse13();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse14();
    /**
     * 批量执行下线入库1(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> executeInWarehouse15();

    // 内部移库出库

    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
     JsonResultVo<Boolean> internalMoveOutTime1();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime2();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime3();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime4();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime5();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime6();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime7();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime8();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime9();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime10();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime11();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime12();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime13();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime14();
    /**
     * 批量执行内部移库出库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveOutTime15();

// 内部移库入库
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
   JsonResultVo<Boolean> internalMoveInTime1();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime2();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime3();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime4();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime5();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime6();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime7();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime8();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime9();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime10();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime11();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime12();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime13();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime14();
    /**
     * 批量执行内部移库入库(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    JsonResultVo<Boolean> internalMoveInTime15();
}
