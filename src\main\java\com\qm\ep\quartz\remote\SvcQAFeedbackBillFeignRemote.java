package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-svc-clm", fallbackFactory = SvcQAFeedbackBillFallbackFactory.class)
public interface SvcQAFeedbackBillFeignRemote {

    @PostMapping("/clmBill/autoCreateMarketreport")
    JsonResultVo<List<Map<String, String>>> autoCreateMarketreport(@RequestHeader("tenantId") String tenantId);

    /**
     * 质量反馈单自动机审
     */
    @PostMapping("/qAFeedbackBill/svcQAFeedbackAutoAudit")
    JsonResultVo auditQAFeedBack(@RequestHeader("tenantId") String tenantId);


    /**
     * 索赔单自动机审
     */
    @PostMapping("/clmBill/clmBillAutoReview")
    JsonResultVo<Object> clmBillAutoReview(@RequestHeader("tenantId") String tenantId);


    /**
     * 索赔单自动机审
     */
    @PostMapping("/clmpicCheck/updateR4")
    JsonResultVo<Object> updateR4(@RequestHeader("tenantId") String tenantId);


    @PostMapping("/clmpicCheck/insertClmCheck")
    JsonResultVo insertClmCheck(@RequestHeader("tenantId") String tenantId,@RequestParam("lasttime") String lasttime);

    /**
     * 维护零件对应责任厂家
     */
    @PostMapping("/spaToDutyVdr/autoSpaTimingTask")
    JsonResultVo<Object> autoSpaTimingTask(@RequestHeader("tenantId") String tenantId);

    /**
     * * 项目定时下发给dms
     */
    @PostMapping("/relateBill/repairItemTimeIssuedForDms")
    JsonResultVo<Object> repairItemTimeIssuedForDms(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);

    @PostMapping("/clmBill/autoFirstExamineMarketreport")
    JsonResultVo<Object> autoFirstExamineMarketreport(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> map);

    /**
     * * 工时单价定时下发给dms
     */
    @PostMapping("/relateBill/workPriceTimeIssuedForDms")
    JsonResultVo<Object> workPriceTimeIssuedForDms(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);

    @PostMapping("/relateBill/clmFrequency")
    JsonResultVo<Object> clmFrequency(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/sync/syncWarrantyPolicy")
    JsonResultVo syncWarrantyPolicy(@RequestHeader("tenantId") String tenantId, @RequestParam("nCompanyId") String nCompanyId, @RequestParam("vLastDtstamp") String vLastDtstamp);

}
