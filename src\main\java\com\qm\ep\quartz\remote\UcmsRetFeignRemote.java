package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**l
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "uc-service-ret", fallbackFactory = UcmsRetFeignFallbackFactory.class)
public interface UcmsRetFeignRemote {

    /**
     * 发票验真
     */
    @PostMapping("/retFeign/invoiceVerify")
    JsonResultVo invoiceVerify(@RequestHeader("tenantId") String tenantId);


    /**
     * 自动入账
     */
    @PostMapping("/retFeign/applyAccountTask")
    JsonResultVo applyAccountTask(@RequestHeader("tenantId") String tenantId,@RequestHeader("companyId") String companyId);

}
