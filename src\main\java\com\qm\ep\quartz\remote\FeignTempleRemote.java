package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.bean.MessageRunTimeJobPO;
import com.qm.ep.quartz.domain.dto.MessageRunTimeJobDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * ly add 20210630
 */
@Repository
@FeignClient(name = "common-service-message-temple", fallbackFactory = FeignFallbackFactory.class)
public interface FeignTempleRemote {
    @PostMapping("/notice/sendNoticeJob")
    JsonResultVo<List<MessageRunTimeJobPO>> sendNoticeJob(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestHeader("userId") String userId);

    @PostMapping("/MessageRunTimeJob/getMessageRunTimeJobListForRun")
    JsonResultVo<List<MessageRunTimeJobPO>> getMessageRunTimeJobListForRun(@RequestHeader("tenantId") String tenantId, @RequestBody MessageRunTimeJobDTO messageRunTimeJobDTO);

}
