package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class TdsSpaRemoteHystrix extends QmRemoteHystrix<TdsSpaRemote> implements TdsSpaRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Object> calculationWarehouse(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.tdsSpaRemoteHystrix.calculationWarehouse");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }


}
