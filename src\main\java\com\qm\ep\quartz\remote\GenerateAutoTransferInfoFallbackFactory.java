package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @CreateTime 2020-09-30 8:32
 * @Version 1.0
 */
@Component
@Slf4j
public class GenerateAutoTransferInfoFallbackFactory implements FallbackFactory<GenerateAutoTransferInfoRemote> {
    @Override
    public GenerateAutoTransferInfoRemote create(Throwable throwable) {
        GenerateAutoTransferInfoRemoteHystrix generateAutoTransferInfoRemoteHystrix = new GenerateAutoTransferInfoRemoteHystrix();
        generateAutoTransferInfoRemoteHystrix.setHystrixEx(throwable);
        return generateAutoTransferInfoRemoteHystrix;
    }
}
