package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class FinancingTransferFeignRemoteHystrix extends QmRemoteHystrix<FinancingTransferFeignRemote> implements FinancingTransferFeignRemote {

    @Override
    public JsonResultVo<Object> financingTransfer(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> financingTransferF(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> uploadBaiwangInvoice(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> preBaiInvoice(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> marry(String tenantId) {
        return getResult();
    }

}
