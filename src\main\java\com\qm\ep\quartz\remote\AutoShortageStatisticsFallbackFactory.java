package com.qm.ep.quartz.remote;


import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AutoShortageStatisticsFallbackFactory implements FallbackFactory<AutoShortageStatisticsRemote> {

    @Override
    public AutoShortageStatisticsRemote create(Throwable throwable) {
        AutoShortageStatisticsRemoteHystrix priceremotehystrix = new AutoShortageStatisticsRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
