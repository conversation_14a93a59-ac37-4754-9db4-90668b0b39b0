package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * Mes-Spt下线入库接口
 *
 * @author: jianghong
 * @time: 2021/4/10 16:04
 */
public interface Itf15MesToSptService {
    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001
     */
    JsonResultVo<Object> transferDataFormXMZ(String loginKey);
    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 繁荣工厂
     */
    JsonResultVo<Object> transferDataFormXMZFR(String loginKey);

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 蔚山2工厂
     */
    JsonResultVo<Object> transferDataFormXMZWS2(String loginKey);

    /**
     * 定时任务 - 从恒展Mes系统获取已下线入库车辆信息 002
     */
    JsonResultVo<Object> transferDataFormHZ(String loginKey);

    /**
     * 定时任务 - 从启明Mes系统获取已下线入库车辆信息 003
     */
    JsonResultVo<Object> transferDataFormQM(String loginKey);

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004
     */
    JsonResultVo<Object> insertMiddleTableFormXMZ(String loginKey);
    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004--繁荣工厂
     */
    JsonResultVo<Object> insertMiddleTableFormXMZFR(String loginKey);

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004--蔚山2工厂
     */
    JsonResultVo<Object> insertMiddleTableFormXMZWS2(String loginKey);
    /**
     * 定时任务 - 从恒展Mes查询数据插入EP中间表 005
     */
    JsonResultVo<Object> insertMiddleTableFormHZ(String loginKey);

    /**
     * 定时任务 - 从启明Mes查询数据插入EP中间表 006
     */
    JsonResultVo<Object> insertMiddleTableFormQM(String loginKey);

    /**
     * 定时任务 - 从ep中间表插入sptb021 007
     */
    JsonResultVo<Object> executeItf15_Mes_Spt_XXRK(String loginKey);

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理)插入到ep中间表 008
     */
    JsonResultVo<Object> insertHgzDataFormXMZWS2(String loginKey);

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 009
     */
    JsonResultVo<Object> transferHgzDataFormXMZWS2(String loginKey);


    /**
     * 定时任务 - 定时任务 - 从繁荣工厂获取插入到ep中间表 010
     */
    JsonResultVo<Object> insertEngineChangeDataFormXMZFR(String loginKey);

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 011
     */
    JsonResultVo<Object> transferEngineChangeDataFormXMZFR(String loginKey);

}
