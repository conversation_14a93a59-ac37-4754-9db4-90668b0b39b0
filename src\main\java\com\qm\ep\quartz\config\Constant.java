package com.qm.ep.quartz.config;

public class Constant {

    private Constant() {
        // 创建一个私有构造函数，解决sonar审查问题。
    }

    //演示系统账户
    public static final String DEMO_ACCOUNT = "test";
    //自动去除表前缀
    public static final String AUTO_REOMVE_PRE = "true";
    //停止计划任务
    public static final String STATUS_RUNNING_STOP = "stop";
    //开启计划任务
    public static final String STATUS_RUNNING_START = "start";
    //通知公告阅读状态-未读
    public static final String OA_NOTIFY_READ_NO = "0";
    //通知公告阅读状态-已读
    public static final int OA_NOTIFY_READ_YES = 1;
    //部门根节点id
    public static final Long DEPT_ROOT_ID = 0l;
    //缓存方式
    public static final String CACHE_TYPE_REDIS = "redis";
    //错误提示
    public static final String LOG_ERROR = "error";
}
