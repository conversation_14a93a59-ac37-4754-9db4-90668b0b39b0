package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.MonthBalanceDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@Component
@FeignClient(name = "tds-service-spa-manainvc",fallbackFactory = MonthBalanceRemoteFallBackFactory.class)
public interface MonthBalanceRemote {

    @PostMapping("/monthBalance/monthBalance")
    JsonResultVo monthBalance(@RequestHeader("tenantId") String tenantId,@RequestBody MonthBalanceDTO paramsDTO);

    /**
     *【EP->TDS】备件采购发票索引明细接口
     * @param tenantId
     * @return
     */
    @PostMapping("/moveBill/syncPurInvoiceIndexToV6")
    JsonResultVo syncPurInvoiceIndexToV6(@RequestHeader("tenantId") String tenantId);

}
