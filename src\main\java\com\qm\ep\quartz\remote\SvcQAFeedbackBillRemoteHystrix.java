package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 定时任务 - 质量反馈机审
 */
@Component
@Slf4j
public class SvcQAFeedbackBillRemoteHystrix extends QmRemoteHystrix<SvcQAFeedbackBillFeignRemote> implements SvcQAFeedbackBillFeignRemote {
    @Override
    public JsonResultVo<List<Map<String, String>>> autoCreateMarketreport(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> auditQAFeedBack(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> clmBillAutoReview(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> updateR4(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo insertClmCheck(String tenantId, String lasttime) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autoSpaTimingTask(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> repairItemTimeIssuedForDms(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autoFirstExamineMarketreport(String tenantId, Map<String, String> map) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> workPriceTimeIssuedForDms(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> clmFrequency(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo syncWarrantyPolicy(String tenantId, String nCompanyId, String vLastDtstamp) {
        return getResult();
    }
}
