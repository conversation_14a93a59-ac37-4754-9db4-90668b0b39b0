package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**l
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "tds-service-svc-pdi", fallbackFactory = FeignFallbackFactory.class)
public interface FeignRemote {

    /**
     * 售前PDI自动机审
     */
    @PostMapping("/automataReview/review")
    JsonResultVo review(@RequestHeader("tenantId")String tenantId);



}
