package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 定时任务 - 向DCEP传输车型车系
 */

@Component
@Slf4j
public class SendProductInfoFallbackFactory implements FallbackFactory<SendProductInfoRemote> {

    @Override
    public SendProductInfoRemote create(Throwable throwable) {
        SendProductInfoRemoteHystrix sendProductInfoRemoteHystrix = new SendProductInfoRemoteHystrix();
        sendProductInfoRemoteHystrix.setHystrixEx(throwable);
        return sendProductInfoRemoteHystrix;
    }
}
