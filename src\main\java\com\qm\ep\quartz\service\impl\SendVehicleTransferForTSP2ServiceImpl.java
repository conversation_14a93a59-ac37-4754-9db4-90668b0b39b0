package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendVehicleTransferForTSP2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendVehicleTransferForTSP2ServiceImpl implements SendVehicleTransferForTSP2Service {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendVehicleTransferForTSP2() {
        return customRemote.sendVehicleTransferForTSP2("15", "6000");
    }

}
