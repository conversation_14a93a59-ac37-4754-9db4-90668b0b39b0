package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WorkFlowLogFallbackFactory implements FallbackFactory<WorkFlowLogRemote> {
    @Override
    public WorkFlowLogRemote create(Throwable throwable) {
        WorkFlowLogRemoteHystrix workFlowLogRemoteHystrix = new WorkFlowLogRemoteHystrix();
        workFlowLogRemoteHystrix.setHystrixEx(throwable);
        return workFlowLogRemoteHystrix;
    }
}
