package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 -  备件模块自动计算季节件+非季节
 */
@Component
@Slf4j
@Data
public class WmsInterfaceRemoteHystrix extends QmRemoteHystrix<WmsInterfaceRemoteHystrix> implements WmsInterfaceRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 自动生成价格
     *
     * @param tenantId
     * @param vitfno
     */
    @Override
    public JsonResultVo<Object> spaMasterDocumentIssued(String tenantId, String vitfno) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.wmsInterfaceRemoteHystrix.spaMasterDocumentIssued");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - wms收货单下发数据下发
     *
     * @param tenantId
     * @param vitfno
     */
    @Override
    public JsonResultVo<Object> receiptIssuedInterface(String tenantId, String vitfno) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.wmsInterfaceRemoteHystrix.receiptIssuedInterface");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - wms提货单下发数据下发   IF-BS-WMS-SPA-IN-002
     *
     * @param tenantId
     * @param vitfno
     */
    @Override
    public JsonResultVo<Object> spaBillOfLadingIssued(String tenantId, String vitfno) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.WmsInterfaceRemoteHystrix.spaBillOfLadingIssued");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> compareInventory(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.WmsInterfaceRemoteHystrix.compareInventory");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> inventoryChange(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.WmsInterfaceRemoteHystrix.inventoryChange");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * create by zhenghaibing on 2021-06-01
     * desc: 车型选配接口 2.2同步精品配件信息 OTD将数据推送给OTD
     * 定时任务： vitfno  IF-OTD-ORDER-SPA-OUT-001
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> syncBoutiqueData(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.WmsInterfaceRemoteHystrix.syncBoutiqueData");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

}
