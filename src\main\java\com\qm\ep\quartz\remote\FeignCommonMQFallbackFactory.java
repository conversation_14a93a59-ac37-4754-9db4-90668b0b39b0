package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2020/09/14 下午 6:27
 * @Description
 * @since 1.0
 */
@Component
@Slf4j
public class FeignCommonMQFallbackFactory implements FallbackFactory<FeignCommonWfRemote> {
    @Override
    public FeignCommonWfRemote create(Throwable throwable) {
        FeignCommonRemoteHystrix feignCommonRemoteHystrix = new FeignCommonRemoteHystrix();
        feignCommonRemoteHystrix.setHystrixEx(throwable);
        return feignCommonRemoteHystrix;
    }
}
