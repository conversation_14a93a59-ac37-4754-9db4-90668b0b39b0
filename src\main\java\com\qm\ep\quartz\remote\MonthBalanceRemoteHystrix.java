package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.MonthBalanceDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import org.springframework.stereotype.Component;

@Component
@Data
public class MonthBalanceRemoteHystrix extends QmRemoteHystrix<MonthBalanceRemote> implements MonthBalanceRemote {
    @Override
    public JsonResultVo monthBalance(String tenantId, MonthBalanceDTO paramsDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo syncPurInvoiceIndexToV6(String tenantId) {
        return getResult();
    }
}
