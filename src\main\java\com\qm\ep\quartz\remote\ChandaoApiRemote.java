package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Repository
@FeignClient(name = "tds-service-logs", fallbackFactory =ChandaoApiFallbackFactory.class)
public interface ChandaoApiRemote {

    @PostMapping("/chanDaoApi/toUpdateAll")
    JsonResultVo<Object> toUpdateAll();

    @PostMapping("/chanDaoApi/taskToUpdateAll")
    JsonResultVo<Object> taskToUpdateAll();

    @PostMapping("/esIndex/clearEsIndex")
    JsonResultVo<Object> clearEsIndex(@RequestParam("specialServiceName") String  specialServiceName);
}
