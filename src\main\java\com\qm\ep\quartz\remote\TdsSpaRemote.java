package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-spa", fallbackFactory = TdsSpaRemoteFallbackFactory.class)
public interface TdsSpaRemote {

    /**
     * 定时任务 - 自动计算新增经销商仓库信息
     */
    @PostMapping("/spaDealerSpareparts/calculationWarehouse")
    JsonResultVo<Object> calculationWarehouse(@RequestHeader("tenantId") String tenantId);
}
