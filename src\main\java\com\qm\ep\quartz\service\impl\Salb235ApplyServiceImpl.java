package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.Salb235ApplyService;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class Salb235ApplyServiceImpl implements Salb235ApplyService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public void testDriveApplySubmitHandle(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        financeTransferRemote.testDriveApplySubmitHandle(loginKeyDO.getTenantId(), loginKeyDO.getCompanyId());
    }

    @Override
    public void testDriveApplyAggregationHandle(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        financeTransferRemote.testDriveApplyAggregationHandle(loginKeyDO.getTenantId(), loginKeyDO.getCompanyId());
    }

    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
