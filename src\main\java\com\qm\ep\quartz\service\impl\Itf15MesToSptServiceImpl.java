package com.qm.ep.quartz.service.impl;


import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15MesToSptRemote;
import com.qm.ep.quartz.service.Itf15MesToSptService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * MesToSpt 接口
 *
 * @author: jianghong
 * @time: 2021/4/10 16:04
 */
@Service
public class Itf15MesToSptServiceImpl implements Itf15MesToSptService {

    @Autowired
    Itf15MesToSptRemote itf15MesToSptRemote;

    /**
     * 定时任务 - 001 从西门子Mom系统获取已下线入库车辆信息
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZ(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferDataFormXMZ(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 - 001 从西门子Mom系统获取已下线入库车辆信息--繁荣工厂
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZFR(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferDataFormXMZFR(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 001 从西门子Mom系统获取已下线入库车辆信息--蔚山2工厂
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZWS2(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferDataFormXMZWS2(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  002 从恒展Mes系统获取已下线入库车辆信息
     */
    @Override
    public JsonResultVo<Object> transferDataFormHZ(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferDataFormHZ(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  003 从启明Mes系统获取已下线入库车辆信息
     */
    @Override
    public JsonResultVo<Object> transferDataFormQM(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferDataFormQM(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  004 从西门子Mom查询数据插入EP中间表
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZ(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertMiddleTableFormXMZ(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }
    /**
     * 定时任务 -  004 从西门子Mom查询数据插入EP中间表-- 繁荣工厂
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZFR(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertMiddleTableFormXMZFR(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  004 从西门子Mom查询数据插入EP中间表-- 蔚山2工厂
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZWS2(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertMiddleTableFormXMZWS2(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  005 从恒展Mes查询数据插入EP中间表
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormHZ(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertMiddleTableFormHZ(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 -  006 从启明Mes查询数据插入EP中间表
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormQM(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertMiddleTableFormQM(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 007 从ep中间表插入sptb021
     */
    @Override
    public JsonResultVo<Object> executeItf15_Mes_Spt_XXRK(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.executeItf15_Mes_Spt_XXRK(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理)插入到ep中间表 008
     */
    @Override
    public JsonResultVo<Object> insertHgzDataFormXMZWS2(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertHgzDataFormXMZWS2(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 009
     */
    @Override
    public JsonResultVo<Object> transferHgzDataFormXMZWS2(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferHgzDataFormXMZWS2(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 定时任务 - 从繁荣工厂获取插入到ep中间表 010
     */
    @Override
    public JsonResultVo<Object> insertEngineChangeDataFormXMZFR(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.insertEngineChangeDataFormXMZFR(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 011
     */
    @Override
    public JsonResultVo<Object> transferEngineChangeDataFormXMZFR(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15MesToSptRemote.transferEngineChangeDataFormXMZFR(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
