package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.domain.bean.Mdac315CDO;
import com.qm.ep.quartz.domain.bean.Mdac315DDO;
import com.qm.ep.quartz.domain.dto.Mdac315CDListDTO;
import com.qm.ep.quartz.domain.dto.Mdac315MiddleDTO;
import com.qm.ep.quartz.remote.SendSALB061ToV2Remote;
import com.qm.ep.quartz.service.SendSALB061ToV2Service;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Service
@Slf4j
public class SendSALB061ToV2ServiceImpl implements SendSALB061ToV2Service {
    @Autowired
    private SendSALB061ToV2Remote sendSALB061ToV2Remote;
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo sendDataToV2() {
        return sendSALB061ToV2Remote.sendDataToV2("15");
    }

    @Override
    public JsonResultVo transferSalb064ToTdsv2() {
        return sendSALB061ToV2Remote.transferSalb064ToTdsv2("15");
    }

    //向DMS发送省市县
    @Override
    public JsonResultVo sendProvinceCityToDMS() {
        return sendSALB061ToV2Remote.sendProvinceCityToDMS("15", "6000");
    }

    //向DMS发送经销商信息
    @Override
    public JsonResultVo sendDealerInfoToDMS() {
        return sendSALB061ToV2Remote.sendDealerInfoToDMS("15", "6000");
    }

    //自动执行大客户资源申请资源分配 38365
    @Override
    public JsonResultVo<Object> mdac315cResourceAllocation() {
        JsonResultVo<Object> result = new JsonResultVo<>();
        //取审核状态数据，含主表及子表
        JsonResultVo<Mdac315CDListDTO> resultVo = sendSALB061ToV2Remote.getMdac315CAuditData("15", "6000");
        if (resultVo.getCode() != 200) {
            throw new QmException(resultVo.getMsg());
        }

        List<Mdac315CDO> mainList = resultVo.getData().getMainList();
        List<Mdac315DDO> detailList = resultVo.getData().getChildList();
        if (!mainList.isEmpty() && !detailList.isEmpty()) {
            //循环执行资源分配，防止报错后回滚处理太过复杂
            for (Mdac315CDO mdac315CDO : mainList) {
                List<Mdac315DDO> curDetaiList = detailList.stream().filter(s -> s.getNmainid().equals(mdac315CDO.getId())).collect(Collectors.toList());
                try {
                    Mdac315MiddleDTO tempDTO = new Mdac315MiddleDTO();
                    tempDTO.setMain(mdac315CDO);
                    tempDTO.setChildList(curDetaiList);
                    JsonResultVo<Mdac315CDO> resultVo1 = sendSALB061ToV2Remote.autoResourceAllocation("15", "6000", tempDTO);
                    log.info(resultVo1.getMsg());
                } catch (Exception e) {
                    log.info(e.getMessage());
                    continue;
                }
            }
            String message = i18nUtil.getMessage("MSG.quartz.common.actSuccess");
            result.setMsg(message);
            return result;
        } else {
            if (mainList.isEmpty()) {
                String errorMsg = i18nUtil.getMessage("ERR.quartz.SendSALB061ToV2ServiceImpl.mdac315cResourceAllocation", "mdac315c");
                result.setMsgErr(errorMsg);
            } else {
                String errorMsg = i18nUtil.getMessage("ERR.quartz.SendSALB061ToV2ServiceImpl.mdac315cResourceAllocation", "mdac315d");
                result.setMsgErr(errorMsg);
            }
            return result;
        }
    }

    /**
     * 向DMS发送产品价格
     *
     * @return
     */
    @Override
    public JsonResultVo sendProductPriceToDMS() {
        return sendSALB061ToV2Remote.sendProductPriceToDMS("15", "6000");
    }

    /**
     * 向新能源DMS发送产品价格
     *
     * @return
     */
    @Override
    public JsonResultVo sendProductPriceToDMSXny() {
        return sendSALB061ToV2Remote.sendProductPriceToDMSXny("15", "6000");
    }
}