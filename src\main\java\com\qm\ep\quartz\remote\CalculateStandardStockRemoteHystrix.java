package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 -  备件模块自动计算季节件+非季节
 */
@Component
@Slf4j
@Data
public class CalculateStandardStockRemoteHystrix extends QmRemoteHystrix<CalculateStandardStockRemote> implements CalculateStandardStockRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Object> startScheduleBySSQ(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.startScheduleBySSQ");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 自动分货 --正常件
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoDistribNormal(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribNormal");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 自动分货 --紧急订单
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoDistribUrgent(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribUrgent");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 自动分货 --文创订单
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoDistribCulturalCreation(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribCulturalCreation");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 超期欠货订单作废
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoabolish(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoabolish");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务--库存销售日报表 calculateSpar925
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> calculateSpar925(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.calculateSpar925");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> timedTask(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> paravalue(String tenantId) {
        return getResult();
    }

    /**
     * 定时任务 - 在库品种合理率
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoqueryrate(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoqueryrate");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 续保
     *
     * @param tenantId
     */
    @Override
    public JsonResultVo<Object> autoRenewRatio(String tenantId, String date) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoRenewRatio");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> autoServiceSupport(String tenantId, String date) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoServiceSupport");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> autoFFVComplaint(String tenantId, String date) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoFFVComplaint");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo summary(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> getAak(String tenantId, String date) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> getRecording(String tenantId, String date) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> autoDistribCulturalNew(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribNormal");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> autoDistribCulturalNew2(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribNormal");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> autoDistribCulturalNew22(String tenantId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribNormal");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
