package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.mapper.QuartzLogMapper;
import com.qm.ep.quartz.remote.SvcQAFeedbackBillFeignRemote;
import com.qm.ep.quartz.service.ClmBillAutoReviewService;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/11/11 上午 9:23
 * @Description
 * @since 1.0
 */
@Service
public class ClmBillAutoReviewServiceImpl implements ClmBillAutoReviewService {

    @Autowired
    private SvcQAFeedbackBillFeignRemote svcQAFeedbackBillFeignRemote;

    @Autowired
    QuartzLogMapper quartzLogMapper;

    @Override
    public JsonResultVo<Object> clmBillAutoReview1() {

        return svcQAFeedbackBillFeignRemote.clmBillAutoReview("15");
    }

    @Override
    public JsonResultVo<Object> updateClmpicCheckDO() {
        return svcQAFeedbackBillFeignRemote.updateR4("15");
    }

    @Override
    public JsonResultVo insertClmCheck() {
        String lasttime = quartzLogMapper.getCILastTime();
        if (BootAppUtil.isNullOrEmpty(lasttime)) {
            lasttime = "2021-11-22";
        }
        return svcQAFeedbackBillFeignRemote.insertClmCheck("15",lasttime);
    }

    @Override
    public JsonResultVo<Object> clmFrequency() {
        return svcQAFeedbackBillFeignRemote.clmFrequency("15");
    }
}
