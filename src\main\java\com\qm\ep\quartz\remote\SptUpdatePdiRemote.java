package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = SptUpdatePdiFallbackFactory.class)
public interface SptUpdatePdiRemote {

    /**
     * 更新PDI检查标识(做成定时任务)
     *
     * @author: zhangyanpeng
     * @time: 2021/5/6
     */
    @PostMapping("/sptVehicle/updatePDICheck")
    public JsonResultVo<Boolean> updatePDICheck(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);


    @PostMapping("/mdac302/updateStatusBC")
    public JsonResultVo<Boolean> updateStatusBC(@RequestHeader("tenantId") String tenantId);


}
