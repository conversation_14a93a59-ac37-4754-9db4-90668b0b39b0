package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.FinanceTransferService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FinanceTransferImpl implements FinanceTransferService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * 融资余额自动转款
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo financeTransfer() {
        return financeTransferRemote.financeTransfer("15", "6000");
    }
}
