package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */

@Component
@Slf4j
public class Itf15VlmsToSptFallbackFactory implements FallbackFactory<Itf15VlmsToSptRemote> {

    @Override
    public Itf15VlmsToSptRemote create(Throwable throwable) {
        Itf15VlmsToSptRemoteHystrix priceremotehystrix = new Itf15VlmsToSptRemoteHystrix();
        priceremotehystrix.setHystrixEx(throwable);
        return priceremotehystrix;
    }
}
