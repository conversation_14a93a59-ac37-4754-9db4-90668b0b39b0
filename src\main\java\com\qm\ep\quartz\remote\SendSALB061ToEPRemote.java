package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-fin", fallbackFactory = SendSALB061ToEPFallback.class)
public interface SendSALB061ToEPRemote {
    //V2向EP传输收款数据
    @PostMapping("/salb061/financeForSal")
    JsonResultVo financeForSal();
}
