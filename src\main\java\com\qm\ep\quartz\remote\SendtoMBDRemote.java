package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * l
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = FinanceTransferFallbackFactory.class)
public interface SendtoMBDRemote {

    /**
     * 定时任务 - 下发营销数据调播信息到MBD
     */
    @PostMapping("/interfacembd/sendDbxxDataToMBD")
    JsonResultVo<Boolean> sendDbxxDataToMBD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestParam("addRess") String addRess);

    /**
     * 定时任务 - 下发营销数据零售信息到MBD
     */
    @PostMapping("/interfacembd/sendLsapiDataToMBD")
    JsonResultVo<Boolean> sendLsapiDataToMBD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestParam("addRess") String addRess);

    /**
     * 定时任务 - 下发营销数据库存融资信息到MBD
     */
    @PostMapping("/interfacembd/sendQbkcDataToMBD")
    JsonResultVo<Boolean> sendQbkcDataToMBD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestParam("addRess") String addRess);

    /**
     * 定时任务 - 下发营销数据融资还款信息到MBD
     */
    @PostMapping("/interfacembd/sendRzhkDataToMBD")
    JsonResultVo<Boolean> sendRzhkDataToMBD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestParam("addRess") String addRess);

    /**
     * 定时任务 - 下发营销数据试乘试驾信息到MBD
     */
    @PostMapping("/interfacembd/sendScsjDataToMBD")
    JsonResultVo<Boolean> sendScsjDataToMBD(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestParam("addRess") String addRess);


}
