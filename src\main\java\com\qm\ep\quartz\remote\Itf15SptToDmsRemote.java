package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15SptToDmsFallbackFactory.class)
public interface Itf15SptToDmsRemote {

    /**
     * 定时任务 - 001 SptToDms：传输采购入库数据
     */
    @PostMapping("/itf15_Spt_Dms_Salb027/purchaseConfirmEPAsyn")
    JsonResultVo<Object> purchaseConfirmEPAsyn(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 002 SptToDms：传输采购退库数据
     */
    @PostMapping("/itf15_Spt_Dms_Salb027/purchaseReturnEPAsyn")
    JsonResultVo<Object> purchaseReturnEPAsyn(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);


    /**
     * 定时任务 - 003 DmsToSpt：从dms获取到货确认数据并自动确认
     */
    @PostMapping("/itf15_spt_dms_salb027_failure/executeItf15_spt_dms_salb027_failure")
    JsonResultVo<Object> executeItf15_spt_dms_salb027_failure(@RequestHeader("companyId") String companyId,
                                              @RequestHeader("custGroupId") String custGroupId,
                                              @RequestHeader("tenantId") String tenantId,
                                              @RequestHeader("userId") String userId);
}
