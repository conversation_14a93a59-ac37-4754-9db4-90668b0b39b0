package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.itfCopCarRemote;
import com.qm.ep.quartz.service.itfCopCarService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class itfCopCarImpl implements itfCopCarService {

    @Autowired
    private itfCopCarRemote itfCopCarRemote;

    /**
     * 移动出行销售车辆信息
     *
     * @Param: [tenantId, companyId]
     * @authorjiaorenmei
     * @Date:
     */
    @Override
    public JsonResultVo<Object> sendCopSellCarInfo(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itfCopCarRemote.sendCopSellCarInfo(loginKeyDO.getTenantId(), loginKeyDO.getCompanyId());
    }

    /**
     * 移动出行退车车辆信息
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo<Object> sendCopReturnCarInfo(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itfCopCarRemote.sendCopReturnCarInfo(loginKeyDO.getTenantId(), loginKeyDO.getCompanyId());
    }

    /**
     * 获取loginkey
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) {
            loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        } else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
