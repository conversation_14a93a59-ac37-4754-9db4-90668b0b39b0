package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 经销商库存和提车在途日报生成
 * <AUTHOR>
 * @date 2021/7/9 11:00
 */
@Component
@Slf4j
public class DealerInventoryRptRemoteHystrix extends QmRemoteHystrix<DealerInventoryRptRemote> implements DealerInventoryRptRemote {

    @Override
    public JsonResultVo createInventoryRpt(String tenantId) {
        return getResult();
    }
}
