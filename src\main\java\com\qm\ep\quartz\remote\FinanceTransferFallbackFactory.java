package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 定时任务 - 融资余额自动转款
 */

@Component
@Slf4j
public class FinanceTransferFallbackFactory implements FallbackFactory<FinanceTransferRemote> {

    @Override
    public FinanceTransferRemote create(Throwable throwable) {
        FinanceTransferRemoteHystrix financetransferRemoteHystrix = new FinanceTransferRemoteHystrix();
        financetransferRemoteHystrix.setHystrixEx(throwable);
        return financetransferRemoteHystrix;
    }
}
