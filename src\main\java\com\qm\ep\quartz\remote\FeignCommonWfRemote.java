package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/09/14 下午 6:26
 * @Description
 * @since 1.0
 */
@Repository
@FeignClient(name = "common-wf", fallbackFactory = FeignCommonWfFallbackFactory.class)
public interface FeignCommonWfRemote {

    /**
     * 启动工作流
     *
     * @param wfInfo
     * @return
     */
    @PostMapping("/flowable/processInstance/startBizWorkFlow")
    JsonResultVo startBizWorkFlow(@RequestParam("businessKey") String businessKey,
                                  @RequestParam("wfCode") String wfCode, @RequestBody Map<String, Object> wfInfo);

    /**
     * 按钮完成任务
     * @param requestParams
     * @return
     */
    @PostMapping("/flowable/task/completeByButton")
    JsonResultVo completeByButton(@RequestBody Map<String, Object> requestParams);
}
