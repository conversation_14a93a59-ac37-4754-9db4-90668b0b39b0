package com.qm.ep.quartz.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-09-25 15:09:21
 */
@Data
@Schema(title = "任务实体", description = "任务实体")
public class TaskVO implements Serializable {

    private static final long serialVersionUID = -3977286031804417842L;
    /**
     * id
     */
    @Schema(title = "id", description = "id")
    private String id;
    /**
     * cron表达式
     */
    @Schema(title = "cron表达式", description = "cron表达式")
    private String cronExpression;
    /**
     * 任务调用的方法名
     */
    @Schema(title = "任务调用的方法名", description = "任务调用的方法名")
    private String methodName;
    /**
     * 任务是否有状态
     */
    @Schema(title = "任务是否有状态", description = "任务是否有状态")
    private String isConcurrent;
    /**
     * 任务描述
     */
    @Schema(title = "任务描述", description = "任务描述")
    private String description;
    /**
     * 更新者
     */
    @Schema(title = "更新者", description = "更新者")
    private String updateBy;
    /**
     * 任务执行时调用哪个类的方法 包名+类名
     */
    @Schema(title = "任务执行时调用哪个类的方法 包名+类名", description = "任务执行时调用哪个类的方法 包名+类名")
    private String beanClass;
    /**
     * 创建时间
     */
    @Schema(title = "创建时间", description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 任务状态
     */
    @Schema(title = "任务状态", description = "任务状态")
    private String jobStatus;
    /**
     * 任务分组
     */
    @Schema(title = "任务分组", description = "任务分组")
    private String jobGroup;
    /**
     * 更新时间
     */
    @Schema(title = "更新时间", description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;
    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    private String createBy;

    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    private String createByName;

    /**
     * 创建者
     */
    @Schema(title = "修改者", description = "修改者")
    private String updateByName;
    /**
     * Spring bean
     */
    @Schema(title = "Spring bean", description = "Spring bean")
    private String springBean;
    /**
     * 任务名
     */
    @Schema(title = "任务名", description = "任务名")
    private String jobName;

    @Schema(title = "重叠任务", description = "重叠任务")
    private String isParallel;

}
