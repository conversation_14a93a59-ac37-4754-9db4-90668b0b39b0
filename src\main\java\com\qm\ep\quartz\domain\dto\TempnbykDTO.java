package com.qm.ep.quartz.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

/**
* <p>
* 
* </p>
* <AUTHOR>
* @since 2021-10-21
*/
@Schema(title = "", description = "")
@Data
public class TempnbykDTO extends JsonParamDto {

    private static final long serialVersionUID = 1L;
    @Schema(title = "产权库id", description = "产权库id")
    private String npropertywhid;
    @Schema(title = "移入物理库id", description = "移入物理库id")
    private String nphysicalwhidin;
    @Schema(title = "移出物理库id", description = "移出物理库id")
    private String nphysicalwhidout;
    @Schema(title = "VIN码", description = "VIN码")
    private String vvin;
    @Schema(title = "接收标识", description = "接收标识")
    private String vflag;
    @Schema(title = "在途标识（已经内部移库出库）", description = "在途标识（已经内部移库出库）")
    private String vontheway;
    @Schema(title = "起始", description = "起始")
    private int start;
    @Schema(title = "结束", description = "结束")
    private int end;

}