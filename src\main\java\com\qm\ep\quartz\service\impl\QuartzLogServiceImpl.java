package com.qm.ep.quartz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.mapper.QuartzLogMapper;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.ep.quartz.utils.NumberUtils;
import com.qm.ep.quartz.utils.QuartzUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.service.impl.QmBaseServiceImpl;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Slf4j
@Service
public class QuartzLogServiceImpl extends QmBaseServiceImpl<QuartzLogMapper, QuartzLogDO> implements QuartzLogService {
    @Autowired
    private QuartzLogMapper quartzLogMapper;
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public boolean saveLogByQuartzId(QuartzDO quartzDO) {
        boolean bRet;
        try {
            QuartzLogDO quartzLogDO = new QuartzLogDO();
            quartzLogDO.setJobName(quartzDO.getJobName());
            quartzLogDO.setJobGroup(quartzDO.getJobGroup());

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
            quartzLogDO.setJobStartTime(df.format(new Date()));

            bRet = save(quartzLogDO);
        } catch (Exception ex) {
            log.info("---error--saveLogByQuartzId失败！", ex);
            bRet = false;
        }
        return bRet;
    }

    @Transactional(rollbackFor = Exception.class, propagation = REQUIRES_NEW)
    @Override
    public boolean saveLog(QuartzLogDO logDO) {
        if (ObjectUtils.isEmpty(logDO)) {
            return false;
        }
        return save(logDO);
    }

    /**
     * Job是否正在运行中
     *
     * @param jobName Job名称
     * @return true 正在运行；false 不在运行。
     */
    @Override
    public boolean isJobRunning(String jobName) {
        boolean bFlag = false;

        // 缩小查询范围
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.getSysdateTime());
        calendar.add(Calendar.MONTH, -1);

        // 查询最后不为E的日志
        QueryWrapper<QuartzLogDO> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<QuartzLogDO> lambda = wrapper.lambda();
        lambda.eq(QuartzLogDO::getJobName, jobName);
        lambda.ne(QuartzLogDO::getJobStatus, QuartzUtils.JOB_LOG_ERROR);
        lambda.ge(QuartzLogDO::getJobStartTime, calendar.getTime());
        lambda.orderByDesc(QuartzLogDO::getJobStartTime);

        IPage<QuartzLogDO> page = baseMapper.selectPage(new Page<>(0, 1), wrapper);
        List<QuartzLogDO> logList = page.getRecords();
        if (!CollectionUtils.isEmpty(logList)) {
            QuartzLogDO lastLog = logList.get(0);
            if (QuartzUtils.JOB_LOG_RUNNING.equals(lastLog.getJobStatus())) {
                // 如果最后一条不为E的日志是I，则说明上次Job没有执行完
                bFlag = true;
            }
        }
        return bFlag;
    }

    @Override
    public String selectRepairItemMaxJobDtstamp() {
        return quartzLogMapper.selectRepairItemMaxJobDtstamp();
    }


    /**
     * 删除指定天数之前的日志信息
     *
     * @param day
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResultVo deleteLogsByDay(String day) {
        Integer dayNum = null;
        try {
            dayNum = Integer.valueOf(day);
        } catch (NumberFormatException e) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.common.param2IntFail");
            throw new NumberFormatException(errorMsg + day);
        }

        dayNum = NumberUtils.conversionNegativeVal(dayNum);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.getSysdateTime());
        calendar.add(Calendar.DATE, dayNum);
        String dateStr = DateUtils.format(calendar.getTime(), "yyyy-MM-dd");
        QueryWrapper<QuartzLogDO> wrapper = new QueryWrapper<>();
        wrapper.lt("JobStartTime", dateStr);
        int flag = baseMapper.delete(wrapper);
        JsonResultVo vo = new JsonResultVo();
        String message = i18nUtil.getMessage("MSG.quartz.common.operateSuccess");
        vo.setMsg(message);
        vo.setData(flag > 0);
        return vo;
    }

    @Override
    public boolean deleteLogsByDay2(String day) {
        return (boolean) deleteLogsByDay(day).getData();
    }

    @Override
    /**
     * 获取job执行成功的最大job开始时间戳
     * jobName是带有 % 的
     * @return
     */
    public String selectMaxJobDtstamp(String jobName) {
        return quartzLogMapper.selectMaxJobDtstamp(jobName);
    }

}
