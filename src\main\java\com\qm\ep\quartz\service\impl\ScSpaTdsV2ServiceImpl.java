package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ScSpaFeignRemote;
import com.qm.ep.quartz.service.ScSpaTdsV2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ScSpaTdsV2ServiceImpl implements ScSpaTdsV2Service {

    @Autowired
    private ScSpaFeignRemote feignRemote;

    @Override
    public JsonResultVo<Object> syncToTdsV2() {
        return feignRemote.syncToTdsV2("15");
    }
}
