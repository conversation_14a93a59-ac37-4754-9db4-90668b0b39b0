package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendProductInfoRemote;
import com.qm.ep.quartz.service.SendProductInfoForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendProductInfoForTspImpl implements SendProductInfoForTspService {

    @Autowired
    private SendProductInfoRemote sendProductInfoRemote;

    @Override
    public JsonResultVo sendProductInfoForTsp() {
        return sendProductInfoRemote.sendProductInfoForTsp("15", "6000");
    }


}
