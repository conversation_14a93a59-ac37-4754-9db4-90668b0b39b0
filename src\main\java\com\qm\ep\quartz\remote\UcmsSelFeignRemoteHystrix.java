package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class UcmsSelFeignRemoteHystrix extends QmRemoteHystrix<UcmsSelFeignRemote> implements UcmsSelFeignRemote {

    /**
     * 定时任务自动下架
     * add by zhoul
     */
    @Override
    public JsonResultVo doOffAutoTask(String tenantId) {
        return getResult();
    }
}
