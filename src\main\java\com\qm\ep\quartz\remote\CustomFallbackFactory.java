package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */

@Component
@Slf4j
public class CustomFallbackFactory implements FallbackFactory<CustomRemote> {

    @Override
    public CustomRemote create(Throwable throwable) {
        CustomRemoteHystrix customRemoteHystrix = new CustomRemoteHystrix();
        customRemoteHystrix.setHystrixEx(throwable);
        return customRemoteHystrix;
    }
}
