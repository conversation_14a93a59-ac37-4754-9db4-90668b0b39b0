package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface ProductSpectrumService {
    /**
     * 向fbom传送产品信息
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */

    JsonResultVo productSpectrum();

    /**
     * 补充处理产品信息
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */

    JsonResultVo handleProductInfo(String loginKey);

    /**
     * @Description: 定时下发产品给三方接口
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2023/6/2
     */
    JsonResultVo<Object> sendPoructInfaceMdac008();
}
