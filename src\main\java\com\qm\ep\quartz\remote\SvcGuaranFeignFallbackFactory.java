package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 投诉单定时生成技术服务申请单
 * <AUTHOR>
 */

@Component
@Slf4j
public class SvcGuaranFeignFallbackFactory implements FallbackFactory<SvcGuaranFeignRemote> {

    @Override
    public SvcGuaranFeignRemote create(Throwable throwable) {
        SvcGuaranFeignRemoteHystrix svcGuaranFeignRemoteHystrix = new SvcGuaranFeignRemoteHystrix();
        svcGuaranFeignRemoteHystrix.setHystrixEx(throwable);
        return svcGuaranFeignRemoteHystrix;
    }
}
