package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendCustInfoUpdateForTSP2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendCustInfoUpdateForTSP2ServiceImpl implements SendCustInfoUpdateForTSP2Service {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendCustInfoUpdateForTSP2() {
        return customRemote.sendCustInfoUpdateForTSP2("15", "6000");
    }

}
