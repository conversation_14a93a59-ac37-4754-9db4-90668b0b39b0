package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.domain.dto.MonthBalanceDTO;
import com.qm.ep.quartz.remote.MonthBalanceRemote;
import com.qm.ep.quartz.service.MonthBalanceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MonthBalanceServiceImpl implements MonthBalanceService {

    @Autowired
    private MonthBalanceRemote monthBalanceRemote;

    @Override
    public JsonResultVo monthBalance() {
        MonthBalanceDTO paramsDTO = new MonthBalanceDTO();
        return monthBalanceRemote.monthBalance("15",paramsDTO);
    }

    @Override
    public JsonResultVo syncPurInvoiceIndexToV6() {
        return monthBalanceRemote.syncPurInvoiceIndexToV6("15");
    }
}
