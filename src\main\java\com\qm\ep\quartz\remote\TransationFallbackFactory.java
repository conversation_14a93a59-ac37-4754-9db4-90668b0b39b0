package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TransationFallbackFactory implements FallbackFactory<TransactionRemote> {

    @Override
    public TransactionRemote create(Throwable throwable) {
        TransationHystrix transationHystrix = new TransationHystrix();
        transationHystrix.setHystrixEx(throwable);
        return transationHystrix;
    }
}
