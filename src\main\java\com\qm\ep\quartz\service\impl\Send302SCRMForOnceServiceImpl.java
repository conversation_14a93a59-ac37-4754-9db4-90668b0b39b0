package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.Send302SCRMForOnceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class Send302SCRMForOnceServiceImpl implements Send302SCRMForOnceService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo send302SCRMForOnce() {
        return customRemote.send302SCRMForOnce("15","6000");
    }

}
