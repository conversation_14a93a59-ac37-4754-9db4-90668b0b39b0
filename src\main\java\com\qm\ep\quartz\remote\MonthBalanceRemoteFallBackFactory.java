package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class MonthBalanceRemoteFallBackFactory implements FallbackFactory<MonthBalanceRemote> {
    @Override
    public MonthBalanceRemote create(Throwable throwable) {
        MonthBalanceRemoteHystrix monthBalanceRemoteHystrix = new MonthBalanceRemoteHystrix();
        monthBalanceRemoteHystrix.setHystrixEx(throwable);
        return monthBalanceRemoteHystrix;
    }
}
