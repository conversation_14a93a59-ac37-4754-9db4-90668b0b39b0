package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SpaPurFeignRemote;
import com.qm.ep.quartz.remote.SpaSaleFeignRemote;
import com.qm.ep.quartz.service.SpaPurfinalService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SpaPurfinalServiceImpl implements SpaPurfinalService {

    @Autowired
    private SpaPurFeignRemote feignRemote;

    @Autowired
    private SpaSaleFeignRemote salefeignRemote;

    @Override
    public JsonResultVo PurstartBySSQ() {
        log.info("采购合同终审");
        return feignRemote.finalJudgmeByDate("15");
    }
    @Override
    public JsonResultVo AutoFormSaleOrder() {
        return salefeignRemote.AutoFormSaleOrder("15");
    }

    /**
     *  定时任务维护收货通知单 申请作废 超过24小时短信发送
     * @return
     */
    @Override
    public JsonResultVo sendSMSByTwentyFourHours() {
        return salefeignRemote.sendSMSByTwentyFourHours("15");
    }

    @Override
    public JsonResultVo sendSMSByFortyEightHours() {
        return salefeignRemote.sendSMSByFortyEightHours("15");
    }
    @Override
    public JsonResultVo newAlgorithmCalculateNbo() {
        return salefeignRemote.newAlgorithmCalculateNbo("15");
    }

}
