package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.sendVehicleInfoForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendVehicleInfoForTsp implements sendVehicleInfoForTspService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public JsonResultVo sendVehicleInfoForTsp() {
        return financeTransferRemote.sendVehicleInfoForTsp("15", "6000");
    }
}
