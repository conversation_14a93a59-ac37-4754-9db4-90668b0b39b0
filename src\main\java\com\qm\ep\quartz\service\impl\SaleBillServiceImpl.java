package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.SaleBillService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SaleBillServiceImpl implements SaleBillService {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    @Override
    public JsonResultVo sendBillToV2() {
        return financeTransferRemote.sendBillToV2("15", "6000");
    }
}
