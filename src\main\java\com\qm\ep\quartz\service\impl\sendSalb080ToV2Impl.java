package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FinanceTransferRemote;
import com.qm.ep.quartz.service.sendSalb080ToV2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendSalb080ToV2Impl implements sendSalb080ToV2Service {

    @Autowired
    private FinanceTransferRemote financeTransferRemote;

    /**
     * ep向v2传送结算单
     *
     * @Param: [tenantId, companyId]
     * <AUTHOR>
     * @Date:
     */
    @Override
    public JsonResultVo sendSalb080ToV2() {
        return financeTransferRemote.sendSalb080ToV2("15", "6000");
    }
}
