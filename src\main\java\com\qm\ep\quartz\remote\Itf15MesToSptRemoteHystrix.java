package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - MesToSpt Mes生产下线接口
 */
@Component
@Slf4j
@Data
public class Itf15MesToSptRemoteHystrix extends QmRemoteHystrix<Itf15MesToSptRemote> implements Itf15MesToSptRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZ(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferDataFormXMZ".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 繁荣工厂
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZFR(String companyId,
                                                      String custGroupId,
                                                      String tenantId,
                                                      String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferDataFormXMZFR".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 蔚山2工厂
     */
    @Override
    public JsonResultVo<Object> transferDataFormXMZWS2(String companyId,
                                                      String custGroupId,
                                                      String tenantId,
                                                      String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferDataFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从恒展Mes系统获取已下线入库车辆信息 002
     */
    @Override
    public JsonResultVo<Object> transferDataFormHZ(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferDataFormHZ".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从启明Mes系统获取已下线入库车辆信息 003
     */
    @Override
    public JsonResultVo<Object> transferDataFormQM(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferDataFormQM".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZ(String companyId,
                                                         String custGroupId,
                                                         String tenantId,
                                                         String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertMiddleTableFormXMZ".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004-- 繁荣工厂
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZFR(String companyId,
                                                           String custGroupId,
                                                           String tenantId,
                                                           String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertMiddleTableFormXMZFR".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004-- 蔚山2工厂
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormXMZWS2(String companyId,
                                                           String custGroupId,
                                                           String tenantId,
                                                           String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertMiddleTableFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从恒展Mes查询数据插入EP中间表 005
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormHZ(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertMiddleTableFormHZ".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从启明Mes查询数据插入EP中间表 006
     */
    @Override
    public JsonResultVo<Object> insertMiddleTableFormQM(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertMiddleTableFormQM".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从ep中间表插入sptb021 007
     */
    @Override
    public JsonResultVo<Object> executeItf15_Mes_Spt_XXRK(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:executeItf15_Mes_Spt_XXRK".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理)插入到ep中间表 008
     */
    @Override
    public JsonResultVo<Object> insertHgzDataFormXMZWS2(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertHgzDataFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 009
     */
    @Override
    public JsonResultVo<Object> transferHgzDataFormXMZWS2(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferHgzDataFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理)插入到ep中间表 008
     */
    @Override
    public JsonResultVo<Object> insertEngineChangeDataFormXMZFR(String companyId,
                                                        String custGroupId,
                                                        String tenantId,
                                                        String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:insertHgzDataFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 009
     */
    @Override
    public JsonResultVo<Object> transferEngineChangeDataFormXMZFR(String companyId,
                                                          String custGroupId,
                                                          String tenantId,
                                                          String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "MesToSpt:transferHgzDataFormXMZWS2".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

}
