package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.AutoShortageStatisticsRemote;
import com.qm.ep.quartz.service.AutoShortageStatisticsService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AutoShortageStatisticsServiceImpl  implements AutoShortageStatisticsService {

    @Autowired
    private AutoShortageStatisticsRemote feignRemote;
    @Override
    public JsonResultVo AutoShortageStatistics() {
        return feignRemote.AutoShortageStatistics("15");
    }

    @Override
    public JsonResultVo autoSummarizesStatisticsData() {
        return feignRemote.autoSummarizesStatisticsData("15");
    }

    @Override
    public JsonResultVo generateCgddGzData() {
        return this.feignRemote.generateCgddGzData("15");
    }
}
