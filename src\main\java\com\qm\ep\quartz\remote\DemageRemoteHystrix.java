package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动把没维护供应商服务页的插入服务页
 */
@Component
@Slf4j
public class DemageRemoteHystrix extends QmRemoteHystrix<DemageRemote> implements DemageRemote {
    @Override
    public JsonResultVo<Object> generate(String tenantId) {
        return getResult();
    }
}
