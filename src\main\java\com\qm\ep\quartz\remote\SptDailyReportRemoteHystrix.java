package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class SptDailyReportRemoteHystrix extends QmRemoteHystrix<SptDailyReportRemote> implements SptDailyReportRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - 001 生成公司库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo<Object> insertSalb026DDO(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptDailyReportRemoteHystrix.insertSalb026DDO");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 002 生成在途明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo<Object> insertSptb022DDO(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptDailyReportRemoteHystrix.insertSptb022DDO");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 003 生成经销商库存明细日报(每日7：50左右生成，时间取决下线入库下夜班时间)
     */
    @Override
    public JsonResultVo<Object> insertSalb027DDO(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptDailyReportRemoteHystrix.insertSalb027DDO");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - 004 使生效的运费转存到当前表(每日0：10左右生成)
     */
    @Override
    public JsonResultVo<Object> transferCurrentSptc072(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptDailyReportRemoteHystrix.transferCurrentSptc072");
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
