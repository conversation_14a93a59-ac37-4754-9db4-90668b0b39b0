package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.SendCustInfoUpdateForTSPService;
import com.qm.ep.quartz.service.SendEVForTspService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SendEVForTspServiceImpl implements SendEVForTspService {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendEVForTsp() {
        return customRemote.sendEVForTsp("15");
    }

}
