package com.qm.ep.quartz.task.extend;

import com.qm.ep.quartz.task.WelcomeJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 同步操作Job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/13
 */
@Slf4j
@Component
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class SynchronousWelJob extends WelcomeJob {

    @SneakyThrows
    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        super.execute(arg0);
        if ("deleteLogsByDay2".equals(arg0.getJobDetail().getKey().getName())) {
            TimeUnit.SECONDS.sleep(5);
        }
    }
}
