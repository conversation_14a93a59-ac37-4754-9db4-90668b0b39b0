ERR.quartz.common.exception=Abnormal:
MSG.quartz.common.success=Succeeded
MSG.quartz.common.actSuccess=Execution succeed!
MSG.quartz.common.operateSuccess=Operation succeeded!
ERR.quartz.common.operateFail=Operation failed!
MSG.quartz.common.delSuccess=Deletion succeeded!
ERR.quartz.common.delFail=Deletion failed!
ERR.quartz.common.getFail=Acquisition failed:
ERR.quartz.common.startTimeNull=Start time should not be null
ERR.quartz.common.endTimeNull=End time should not be null
ERR.quartz.sptUpdatePdiHystrix.updatePDICheck=Please wait patiently while the transaction is automatically voided......
ERR.quartz.calculateStandardStockRemoteHystrix.startScheduleBySSQ=Please wait patiently for the maintenance of the standard inventory calculation service ......
ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribNormal=Please wait patiently for the maintenance of the automatic distribution and normal parts service......
ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribUrgent=Please wait patiently for the maintenance of the automatic distribution and urgent order service......
ERR.quartz.calculateStandardStockRemoteHystrix.autoDistribCulturalCreation=Please wait patiently for the maintenance of automatic distribution and cultural and creative order service......
ERR.quartz.calculateStandardStockRemoteHystrix.autoabolish=Please wait patiently for the maintenance of the overdue order void service......
ERR.quartz.calculateStandardStockRemoteHystrix.calculateSpar925=Please wait patiently for the maintenance of the inventory sales calculation and daily report service......
ERR.quartz.calculateStandardStockRemoteHystrix.autoqueryrate=Please wait patiently for the maintenance of the reasonable rate of library varieties......
ERR.quartz.calculateStandardStockRemoteHystrix.autoRenewRatio=Please wait patiently for the renewal and maintenance......
ERR.quartz.calculateStandardStockRemoteHystrix.autoServiceSupport=Please wait patiently for the support and maintenance by master......
ERR.quartz.calculateStandardStockRemoteHystrix.autoFFVComplaint=Please wait patiently for the maintenance of the one-time repair rate.....
ERR.quartz.autoRebateRemoteHystrix.autoRebate=Please wait patiently for the formation of the sales rebate......
ERR.quartz.wmsInterfaceRemoteHystrix.spaMasterDocumentIssued=Please wait patiently for the issuance of wms spare parts master file......
ERR.quartz.wmsInterfaceRemoteHystrix.receiptIssuedInterface=Please wait patiently for the issuance of wms receipt data......
ERR.quartz.WmsInterfaceRemoteHystrix.spaBillOfLadingIssued=Please wait patiently for the issuance of wms bill of lading data.....
ERR.quartz.WmsInterfaceRemoteHystrix.compareInventory=Please wait patiently for the execution of wms inventory comparison timed task......
ERR.quartz.WmsInterfaceRemoteHystrix.inventoryChange=Please wait patiently for the execution of wms inventory change timed task......
ERR.quartz.WmsInterfaceRemoteHystrix.syncBoutiqueData=Please wait patiently for the execution of OTD synchronization high quality accessories information timed task......
ERR.quartz.SptCheckDataRemoteHystrix.dealNinventoryOfSalb021=Please wait patiently for the maintenance of processing salb021 inventory number dealNinventoryOfSalb021 interface service......
ERR.quartz.SptCheckDataRemoteHystrix.dealNinventoryOfSptb023=Please wait patiently for the maintenance of processing sptb023 inventory number dealNinventoryOfSptb023 interface service......
ERR.quartz.SptCheckDataRemoteHystrix.dealAssignOfSptb023=Please wait patiently for the maintenance of processing sptb023 distribution inventory dealAssignOfSptb023 interface service..........
ERR.quartz.TransationHystrix.common=Please wait patiently for the acquisition of data......
ERR.quartz.TransationHystrix.transationVoid=Please wait patiently while the transaction is automatically voided......
ERR.quartz.TransationHystrix.weekRelease=Please wait patiently for the release of the weekly plan at the end of the month......
ERR.quartz.SptQueryXxrkHystrix.common=Offline inbound failed......
ERR.quartz.SptQueryXxrkHystrix.internalMoveInTime=Internal transfer and inbound failed......
ERR.quartz.SptQueryXxrkHystrix.internalMoveOutTime=Internal transfer and outbound failed......
ERR.quartz.SptDailyReportRemoteHystrix.insertSalb026DDO=Please wait patiently for the maintenance of generating company inventory details daily report insertSalb026DDO interface service......
ERR.quartz.SptDailyReportRemoteHystrix.insertSptb022DDO=Please wait patiently for the maintenance of generating the details daily report in delivery insertSptb022DDO interface service...............
ERR.quartz.SptDailyReportRemoteHystrix.insertSalb027DDO=Please wait patiently for the maintenance of generating dealer inventory details daily report insertSalb027DDO interface service......
ERR.quartz.SptDailyReportRemoteHystrix.transferCurrentSptc072=Please wait patiently for the maintenance of making the effective freight transfer to the current table transferCurrentSptc072 interface service......
ERR.quartz.ScSpaFeignRemoteHystrix.scheduleFbomMasterial=Please wait patiently for the execution of Fbom timed material master data...";
ERR.quartz.ScSpaFeignRemoteHystrix.scheduleFbomVehicleSpa=Please wait patiently for the execution of Fbom timed catalog vehicle model...";
ERR.quartz.ScSpaFeignRemoteHystrix.generate=Please wait patiently for the maintenance of the function that automatically inserts the claim page of the unmaintained spare parts into the claim page...";
ERR.quartz.ScSpaFeignRemoteHystrix.fbomProductTimesInterface=Please wait patiently for the implementation of Fbom timing product frequency......";
ERR.quartz.FeignCommonRemoteHystrix.common=Workflow system maintenance...
ERR.quartz.FinanceRemoteHystrix.finance=Financial interface remote calling failed
ERR.quartz.FinanceRemoteHystrix.feignExeOut=Outbound and inbound execution failed
ERR.quartz.FinanceRemoteHystrix.feignInvoice=Sales invoice maintenance failed
ERR.quartz.hystrix.common=Please wait patiently for maintenance of the interface service......
ERR.quartz.welcomeJob.callMethodNonExist=Function does not exist!
ERR.quartz.jobService.updateFail=Job update failed!
ERR.quartz.DataMonitorServiceImpl.monitorDataByVgroup=Missing data parameter, parameter example: {companyId},{tenantId},{vgroup}, such as: 6000,15,SAL
MSG.quartz.financeCommon.spareFinanceFlag=Spare parts financial interface identifier is 0
MSG.quartz.SendSmsServiceImpl.sendEMailFinish=The timed task of calling the outbox is completed!
ERR.quartz.SendSmsServiceImpl.sendEMailCheckParam=The number of input parameters is not three, please check the input parameters and resend!
ERR.quartz.SendSmsServiceImpl.sendEMailParamEmpty=The input parameter is empty, please check the input parameter and send it again!
ERR.quartz.JobCustomServiceImpl.autVehicleService=Last execution time not obtained!
ERR.quartz.SendSALB061ToV2ServiceImpl.mdac315cResourceAllocation=The main table (%s) of the maintenance VIP customer resource application form is empty
ERR.quartz.common.param2IntFail=Parameter conversion Integer failed, params:
ERR.quartz.TransationHystrix.sendPlanCalerderToJCK=Synchronize the import and export plan calendar. Please wait patiently.........
