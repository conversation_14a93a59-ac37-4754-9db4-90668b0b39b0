package com.qm.ep.quartz.service;

import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.service.IQmBaseService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-15
 */
@Service
public interface QuartzLogService extends IQmBaseService<QuartzLogDO> {
    /**
     * 该方法不建议使用
     * @param quartzDO
     * @return
     */
    @Deprecated
    boolean saveLogByQuartzId(QuartzDO quartzDO);

    /**
     * 保存日志信息
     * @param logDO
     * @return
     */
    boolean saveLog(QuartzLogDO logDO);
    /**
     * Job是否正在运行中
     *
     * @param jobName Job名称
     * @return true 正在运行；false 不在运行。
     */
    boolean isJobRunning(String jobName);

    /**
     * 获取指定job执行成功的最大job开始时间戳
     * @return
     */
    String selectRepairItemMaxJobDtstamp();

    /**
     * 删除指定天数之前的日志信息
     * @param day
     * @return
     */
    JsonResultVo deleteLogsByDay(String day);


    boolean deleteLogsByDay2(String day);

    /**
     * 获取job执行成功的最大job开始时间戳
     * jobName是带有 % 的
     * @return
     */
    String selectMaxJobDtstamp(String jobName);
}
