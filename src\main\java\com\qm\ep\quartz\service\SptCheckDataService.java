package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * @author: jianghong
 * @time: 2021/6/30 10:39
 */
public interface SptCheckDataService {
    /**
     * 定时任务 - SptCheckData：001 处理salb021库存数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    JsonResultVo<Object> dealNinventoryOfSalb021(String loginKey);

    /**
     * 定时任务 - SptCheckData：002 处理sptb023库存数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    JsonResultVo<Object> dealNinventoryOfSptb023(String loginKey);

    /**
     * 定时任务 - SptCheckData：003 处理sptb023分配数
     *
     * @author: jianghong
     * @time: 2021/6/30
     */
    JsonResultVo<Object> dealAssignOfSptb023(String loginKey);

    /**
     * 定时任务 - 同步sptc001
     *
     * @author: hyh
     * @time: 2021/12/10
     */
    JsonResultVo<Boolean> updateSptc001(String loginKey);

}
