package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import org.springframework.stereotype.Component;

@Component
@Data
public class HqNewIntelligentOrderJobHystrix  extends QmRemoteHystrix<HqNewIntelligentOrderJobRemote> implements HqNewIntelligentOrderJobRemote {
    @Override
    public JsonResultVo calculateStockPartMad(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo calculateStockWidth(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo calculateStockPartRequir(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo calculateStockPartParams(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo calculateStockThreeYearsSaleCount(String tenantId) {
        return getResult();
    }
}
