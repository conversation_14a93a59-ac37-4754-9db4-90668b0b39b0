package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface SendProductInfoService {

    //向DCEP传输车型车系
    JsonResultVo sendProductInfoToDCEP();

    //向DMS发送产品信息
    JsonResultVo sendProductInfoToDMS();

    // 红旗H9产品信息与电商中心接口
    JsonResultVo transferProductToDS();


    // 整车所有产品选装包拆分
    JsonResultVo calculateProductPacData();

    // 根据FBOM获得年款车型后主动拉取FBOM得配置信息
    JsonResultVo sendSalesConfigCode();

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    JsonResultVo<Object> dealsysc009dlist(String loginKey);

    /**
     * 给dms同步字典
     *
     * @return
     */
    JsonResultVo<Boolean> sendDictToDms();

    JsonResultVo<Boolean> sendDealerInfoToUtFin();

    JsonResultVo<Boolean> sendProductInfoToUtFin();

    /**
     * @Description: 定时同步tdsv2产品接口数据
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2023/4/27
     */
    JsonResultVo<Object> sendTdsV2mdac008c(String vJson);


    //下发电商中心 市场指导价
        JsonResultVo sendProductPriceToDSZX();
}
