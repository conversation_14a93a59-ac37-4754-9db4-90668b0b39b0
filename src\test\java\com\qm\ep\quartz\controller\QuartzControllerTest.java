package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.dto.QuartzDTO;
import com.qm.ep.quartz.domain.vo.TaskVO;
import com.qm.ep.testapi.constant.UserConstants;
import com.qm.ep.testapi.controller.BaseTestController;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p>
 * ControllerTest
 * 测试类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-21
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class QuartzControllerTest extends BaseTestController<QuartzController> {

    @Before
    public void beforMethod() {
        this.initUser(UserConstants.USER_CODE_COMPANY);
    }

    /**
     * 覆盖dto
     */
    @Test
    public void moduleDtoTest() {
        EqualsVerifier.simple().forClass(QuartzDTO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖vo
     */
    @Test
    public void moduleVoTest() {
        EqualsVerifier.simple().forClass(TaskVO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    /**
     * 覆盖do
     */
    @Test
    public void moduleDoTest() {
        EqualsVerifier.simple().forClass(QuartzDO.class).suppress(Warning.INHERITED_DIRECTLY_FROM_OBJECT).suppress(Warning.ALL_FIELDS_SHOULD_BE_USED).verify();
    }

    @Test
    public void save() {
        QuartzDO bean = this.moduleFill(QuartzDO.class);
        JsonResultVo<QuartzDO> resultVo;

        // 新增数据
        resultVo = this.testController.save(bean);
        this.assertJsonResultVoFalse(resultVo);
        // 删除新增的数据
        resultVo = this.testController.delete(bean.getId());
        this.assertJsonResultVoFalse(resultVo);
    }

    @Test
    public void delete() {
        QuartzDO bean = new QuartzDO();
        JsonResultVo<QuartzDO> resultVo;

        resultVo = this.testController.delete(bean.getId());
        this.assertJsonResultVoFalse(resultVo);
    }

    @Test
    public void selectList() {
        QuartzDTO dto = this.moduleEmpty(QuartzDTO.class);
        JsonResultVo resultVo;

        resultVo = this.testController.selectList(dto);
        this.assertJsonResultVo(resultVo);

        dto = this.moduleFill(QuartzDTO.class);
        resultVo = this.testController.selectList(dto);
        this.assertJsonResultVo(resultVo);
    }

    @Test
    public void selectDistinctGroup() {
        JsonResultVo resultVo;

        resultVo = this.testController.selectDistinctGroup();
        this.assertJsonResultVo(resultVo);
    }

    @Test
    public void selectDistinctName() {
        JsonResultVo resultVo;

        resultVo = this.testController.selectDistinctName();
        this.assertJsonResultVo(resultVo);
    }

    @Test
    public void running() {
        QuartzDO dto = this.moduleEmpty(QuartzDO.class);
        JsonResultVo resultVo;

        try {
            dto.setId(RandomUtils.getRandomID());
            resultVo = this.testController.running(dto);
            this.assertJsonResultVoFalse(resultVo);
        } catch (Exception e) {

        }
    }
}