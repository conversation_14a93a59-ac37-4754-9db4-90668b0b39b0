package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.bean.Mdac315CDO;
import com.qm.ep.quartz.domain.dto.Mdac315CDListDTO;
import com.qm.ep.quartz.domain.dto.Mdac315MiddleDTO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory = SendSALB061ToV2Fallback.class)
public interface SendSALB061ToV2Remote {
    /**
     * 向DMS发送经销商信息
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/salb061/sendDataToV2")
    JsonResultVo sendDataToV2(@RequestHeader("tenantId") String tenantId);

    /**
     * EP-TDSV2信用账户使用凭证接口
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/salb064/transferSalb064ToTdsv2")
    JsonResultVo transferSalb064ToTdsv2(@RequestHeader("tenantId") String tenantId);

    /**
     * 向DMS发送经销商信息
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/interface/sendProvinceCityToDMS")
    JsonResultVo sendProvinceCityToDMS(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 向DMS发送经销商信息
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/interface/sendDealerInfoToDMS")
    JsonResultVo sendDealerInfoToDMS(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 定时任务取大客户资源申请审核数据信息 38365
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/mdac315C/getMdac315CAuditData")
    JsonResultVo<Mdac315CDListDTO> getMdac315CAuditData(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 自动执行大客户资源申请资源分配 38365
     *
     * @param tenantId
     * @param companyId
     * @param tempDTO
     * @return
     */
    @PostMapping("/mdac315C/autoResourceAllocation")
    JsonResultVo<Mdac315CDO> autoResourceAllocation(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId, @RequestBody Mdac315MiddleDTO tempDTO);

    /**
     * 向DMS发送产品价格
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/interface/sendProductPriceToDMS")
    JsonResultVo sendProductPriceToDMS(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

    /**
     * 向新能源DMS发送产品价格
     *
     * @param tenantId
     * @param companyId
     * @return
     */
    @PostMapping("/interface/sendProductPriceToDMSXny")
    JsonResultVo sendProductPriceToDMSXny(@RequestHeader("tenantId") String tenantId, @RequestHeader("companyId") String companyId);

}