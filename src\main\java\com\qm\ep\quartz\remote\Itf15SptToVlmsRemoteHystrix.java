package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - SptToVlms 储运和一汽物流系统接口
 */
@Component
@Slf4j
@Data
public class Itf15SptToVlmsRemoteHystrix extends QmRemoteHystrix<Itf15SptToVlmsRemote> implements Itf15SptToVlmsRemote {
    @Autowired
    private I18nUtil i18nUtil;

    /**
     * 定时任务 - SptToVlms：省区 001
     */
    @Override
    public JsonResultVo<Object> transferDataOfcsqdm(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfcsqdm".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：市 002
     */
    @Override
    public JsonResultVo<Object> transferDataOfcsxdm(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfcsxdm".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：区县 003
     */
    @Override
    public JsonResultVo<Object> transferDataOfcxqdm(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfcxqdm".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：产品代码 004
     */
    @Override
    public JsonResultVo<Object> transferDataOfcpdm(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfcpdm".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：发运计划 005
     */
    @Override
    public JsonResultVo<Object> transferDataOffyjh(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOffyjh".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：删除发运计划 006
     */
    @Override
    public JsonResultVo<Object> transferDataOftfyjh(String companyId,
                                                    String custGroupId,
                                                    String tenantId,
                                                    String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOftfyjh".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：指派车道 007
     */
    @Override
    public JsonResultVo<Object> transferDataOfzpcd(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfzpcd".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：取消指派车道 008
     */
    @Override
    public JsonResultVo<Object> transferDataOfqxzpcd(String companyId,
                                                     String custGroupId,
                                                     String tenantId,
                                                     String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfqxzpcd".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：发运出库 009
     */
    @Override
    public JsonResultVo<Object> transferDataOffyck(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOffyck".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：出库换车 010
     */
    @Override
    public JsonResultVo<Object> transferDataOfckhc(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfckhc".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：车辆到货 011
     */
    @Override
    public JsonResultVo<Object> transferDataOfcldh(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfcldh".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：计划返单 012
     */
    @Override
    public JsonResultVo<Object> transferDataOfjhfd(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfjhfd".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：运费结算 013
     */
    @Override
    public JsonResultVo<Object> transferDataOfyfjs(String companyId,
                                                   String custGroupId,
                                                   String tenantId,
                                                   String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfyfjs".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    /**
     * 定时任务 - SptToVlms：下线入库信息传给一汽物流接口 transferDataOfWms
     */
    @Override
    public JsonResultVo<Object> transferDataOfWms(String companyId,
                                                  String custGroupId,
                                                  String tenantId,
                                                  String userId) {
        String MESSAGE = i18nUtil.getMessage("ERR.quartz.hystrix.common");
        String errorMsg = "SptToVlms:transferDataOfWms".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

}
