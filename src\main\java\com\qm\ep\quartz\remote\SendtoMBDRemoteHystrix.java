package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务 - 融资余额自动转款
 */
@Component
@Slf4j
public class SendtoMBDRemoteHystrix extends QmRemoteHystrix<SendtoMBDRemote> implements SendtoMBDRemote {
    @Override
    public JsonResultVo<Boolean> sendDbxxDataToMBD(String tenantId, String companyID, String addRess) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendLsapiDataToMBD(String tenantId, String companyID, String addRess) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendQbkcDataToMBD(String tenantId, String companyID, String addRess) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendRzhkDataToMBD(String tenantId, String companyID, String addRess) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendScsjDataToMBD(String tenantId, String companyID, String addRess) {
        return getResult();
    }


}
