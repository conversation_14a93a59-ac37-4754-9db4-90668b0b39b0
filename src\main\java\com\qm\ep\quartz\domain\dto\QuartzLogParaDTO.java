package com.qm.ep.quartz.domain.dto;

import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

@Data
@Schema(title = "定时任务基本信息", description = "定时任务基本信息")
public class QuartzLogParaDTO extends JsonParamDto {


    /**
     * 任务分组
     */
    @Schema(title = "任务分组", description = "任务分组")
    private String jobGroup;


    /**
     * 任务名
     */
    @Schema(title = "任务名", description = "任务名")
    private String jobName;


    /**
     * 日期范围
     */
    @Schema(title = "日期范围", description = "日期范围")
    private String[] time;

    /**
     * 开始日期
     */
    @Schema(title = "开始日期", description = "开始日期")
    private String start;

    /**
     * 开始日期
    */
    @Schema(title = "结束日期", description = "结束日期")
    private String end;
}
