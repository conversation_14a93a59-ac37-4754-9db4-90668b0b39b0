package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.FeignRemote;
import com.qm.ep.quartz.service.TestService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TestServiceImpl implements TestService {
    @Autowired
    private FeignRemote feignRemote;

    @Override
    public JsonResultVo pdiAutoAdt() {
        return feignRemote.review("15");
    }
}
