package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class FeignMonitorRemoteHystrix extends QmRemoteHystrix<FeignMonitorRemote> implements FeignMonitorRemote {

    @Override
    public JsonResultVo<Boolean> distributorAverageLastOneMonth(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> distributorAverageLastTwelveMonth(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> doBRScene(String tenantId, String vBillType, String vSvcRBNode, String nBillId, String companyid) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> doBRScene4NoSampledClmBill(@RequestHeader("tenantId") String tenantId) {
        return getResult();
    }
}
