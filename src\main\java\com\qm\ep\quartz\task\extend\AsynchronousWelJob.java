package com.qm.ep.quartz.task.extend;

import com.qm.ep.quartz.task.WelcomeJob;
import com.qm.tds.util.DateUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**异步操作Job
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/13
 */
@Slf4j
@Component
public class AsynchronousWelJob extends WelcomeJob {


    @SneakyThrows
    @Override
    public void execute(JobExecutionContext arg0) throws JobExecutionException {
        super.execute(arg0);
    }
}
