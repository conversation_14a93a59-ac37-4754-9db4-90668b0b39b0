package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务 - 向DCEP传输车型车系
 */
@Component
@Slf4j
public class SendProductInfoRemoteHystrix extends QmRemoteHystrix<SendProductInfoRemote> implements SendProductInfoRemote {
    private static final String MESSAGE = " 接口服务维护中请耐心等待.........";

    @Override
    public JsonResultVo SendProductInfo(String tenantId, String companyId) {
        return null;
    }

    /**
     * 定时任务 - 向fbom传送产品信息
     */
    @Override
    public JsonResultVo productSpectrum(String tenantId, String companyId) {
        return getResult();
    }

    /**
     * 定时任务 - 补充处理产品信息
     */
    @Override
    public JsonResultVo handleProductInfo(String companyId, String custGroupId, String tenantId, String userId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendProductInfoToDMS(String tenantId, String companyId) {
        return getResult();
    }

    /**
     * 红旗H9产品信息与电商中心接口
     *
     * @Param: [tenantId, companyId]
     * @return: com.qm.tds.api.domain.JsonResultVo
     * @Author: 刘伟新
     * @Date: 2021/5/10
     */
    @Override
    public JsonResultVo transferProductToDS(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo calculateProductPacData(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo sendProductInfoForTsp(String tenantId, String companyId) {
        return null;
    }

    /**
     * 定时任务 - 根据FBOM获得年款车型后主动拉取FBOM得配置信息
     */
    @Override
    public JsonResultVo sendSalesConfigCode(String tenantId, String companyId) {
        return getResult();
    }

    /**
     * 定时任务 -字典子表sysc009d同步
     */
    @Override
    public JsonResultVo<Object> dealsysc009dlist(String companyId,
                                                 String custGroupId,
                                                 String tenantId,
                                                 String userId) {
        String errorMsg = "字典子表sysc009d同步dealsysc009dlist".concat(MESSAGE);
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_ERR);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> sendDictToDms(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendDealerInfoToUtFin(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Boolean> sendProductInfoToUtFin(String tenantId, String companyId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendTdsV2mdac008c(String tenantId, String companyId,String vJson) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sendPoructInfaceMdac008(String tenantId, String companyId) {
        return getResult();
    }
}