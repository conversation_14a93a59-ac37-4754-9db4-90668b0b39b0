package com.qm.ep.quartz.remote;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * l
 *
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "tds-service-svc-data", fallbackFactory = PriceFallbackFactory.class)
public interface PriceRemote {

    /**
     * 定时任务 - 自动生成价格
     */
    @PostMapping("/autGeneratePrice/generate")
    JsonResultVo generate(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 每天更新各经销商30日内索赔频次
     */
    @PostMapping("/sysDealerService/calculatefrequency")
    JsonResultVo calculatefrequency(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - 每天更新各经销商30日内反馈频次
     */
    @PostMapping("/sysDealerService/calculatefeedbackfrequency")
    JsonResultVo calculatefbfrequency(@RequestHeader("tenantId") String tenantId);


    /**
     * 定时任务 - 自动把没维护经销商索赔页的插入索赔页
     */
    @PostMapping("/sysDealerService/sysDealerInfo")
    JsonResultVo sysDealerInfo(@RequestHeader("tenantId") String tenantId);
}
