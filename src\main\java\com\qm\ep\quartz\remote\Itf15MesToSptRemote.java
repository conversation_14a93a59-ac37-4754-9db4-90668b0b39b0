package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * <AUTHOR>
 * 定时任务 -  Mes生产下线接口
 */
@Repository
@FeignClient(name = "tds-service-spt", fallbackFactory = Itf15MesToSptFallbackFactory.class)
public interface Itf15MesToSptRemote {

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001
     */
    @PostMapping("/itf15MesToSpt/transferDataFormXMZ")
    JsonResultVo<Object> transferDataFormXMZ(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 繁荣工厂
     */
    @PostMapping("/itf15MesToSpt/transferDataFormXMZFR")
    JsonResultVo<Object> transferDataFormXMZFR(@RequestHeader("companyId") String companyId,
                                             @RequestHeader("custGroupId") String custGroupId,
                                             @RequestHeader("tenantId") String tenantId,
                                             @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 从西门子Mom系统获取已下线入库车辆信息 001-- 蔚山2工厂
     */
    @PostMapping("/itf15MesToSpt/transferDataFormXMZWS2")
    JsonResultVo<Object> transferDataFormXMZWS2(@RequestHeader("companyId") String companyId,
                                               @RequestHeader("custGroupId") String custGroupId,
                                               @RequestHeader("tenantId") String tenantId,
                                               @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从恒展Mes系统获取已下线入库车辆信息 002
     */
    @PostMapping("/itf15MesToSpt/transferDataFormHZ")
    JsonResultVo<Object> transferDataFormHZ(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从启明Mes系统获取已下线入库车辆信息 003
     */
    @PostMapping("/itf15MesToSpt/transferDataFormQM")
    JsonResultVo<Object> transferDataFormQM(@RequestHeader("companyId") String companyId,
                                            @RequestHeader("custGroupId") String custGroupId,
                                            @RequestHeader("tenantId") String tenantId,
                                            @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004
     */
    @PostMapping("/itf15MesToSpt/insertMiddleTableFormXMZ")
    JsonResultVo<Object> insertMiddleTableFormXMZ(@RequestHeader("companyId") String companyId,
                                                  @RequestHeader("custGroupId") String custGroupId,
                                                  @RequestHeader("tenantId") String tenantId,
                                                  @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004-- 繁荣工厂
     */
    @PostMapping("/itf15MesToSpt/insertMiddleTableFormXMZFR")
    JsonResultVo<Object> insertMiddleTableFormXMZFR(@RequestHeader("companyId") String companyId,
                                                  @RequestHeader("custGroupId") String custGroupId,
                                                  @RequestHeader("tenantId") String tenantId,
                                                  @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 从西门子Mom查询数据插入EP中间表 004-- 蔚山2工厂
     */
    @PostMapping("/itf15MesToSpt/insertMiddleTableFormXMZWS2")
    JsonResultVo<Object> insertMiddleTableFormXMZWS2(@RequestHeader("companyId") String companyId,
                                                    @RequestHeader("custGroupId") String custGroupId,
                                                    @RequestHeader("tenantId") String tenantId,
                                                    @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从恒展Mes查询数据插入EP中间表 005
     */
    @PostMapping("/itf15MesToSpt/insertMiddleTableFormHZ")
    JsonResultVo<Object> insertMiddleTableFormHZ(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从启明Mes查询数据插入EP中间表 006
     */
    @PostMapping("/itf15MesToSpt/insertMiddleTableFormQM")
    JsonResultVo<Object> insertMiddleTableFormQM(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);
    /**
     * 定时任务 - 从ep中间表插入sptb021 007
     */
    @PostMapping("/itf15MesToSpt/executeItf15_Mes_Spt_XXRK")
    JsonResultVo<Object> executeItf15_Mes_Spt_XXRK(@RequestHeader("companyId") String companyId,
                                                   @RequestHeader("custGroupId") String custGroupId,
                                                   @RequestHeader("tenantId") String tenantId,
                                                   @RequestHeader("userId") String userId);


    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理)插入到ep中间表 008
     */
    @PostMapping("/itf15MesToSpt/insertHgzDataFormXMZWS2")
    JsonResultVo<Object> insertHgzDataFormXMZWS2(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 009
     */
    @PostMapping("/itf15MesToSpt/transferHgzDataFormXMZWS2")
    JsonResultVo<Object> transferHgzDataFormXMZWS2(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);


    /**
     * 定时任务 - 从繁荣工厂获取插入到ep中间表 010
     */
    @PostMapping("/itf15MesToSpt/insertEngineChangeDataFormXMZFR")
    JsonResultVo<Object> insertEngineChangeDataFormXMZFR(@RequestHeader("companyId") String companyId,
                                                 @RequestHeader("custGroupId") String custGroupId,
                                                 @RequestHeader("tenantId") String tenantId,
                                                 @RequestHeader("userId") String userId);

    /**
     * 定时任务 - 从蔚山2工厂获取合格证信息(非商品车管理) 011
     */
    @PostMapping("/itf15MesToSpt/transferEngineChangeDataFormXMZFR")
    JsonResultVo<Object> transferEngineChangeDataFormXMZFR(@RequestHeader("companyId") String companyId,
                                                   @RequestHeader("custGroupId") String custGroupId,
                                                   @RequestHeader("tenantId") String tenantId,
                                                   @RequestHeader("userId") String userId);

}
