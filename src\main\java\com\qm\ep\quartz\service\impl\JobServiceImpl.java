package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.config.Constant;
import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.domain.other.ScheduleJob;
import com.qm.ep.quartz.domain.vo.TaskVO;
import com.qm.ep.quartz.mapper.QuartzMapper;
import com.qm.ep.quartz.mapper.TaskDao;
import com.qm.ep.quartz.service.JobService;
import com.qm.ep.quartz.utils.QuartzManager;
import com.qm.ep.quartz.utils.ScheduleJobUtils;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

@Slf4j
@Service
public class JobServiceImpl implements JobService {

    @Autowired
    private TaskDao taskScheduleJobMapper;
    @Autowired
    QuartzManager quartzManager;
    @Autowired(required = false)
    QuartzMapper quartzMapper;
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public TaskVO get(String id) {
        return taskScheduleJobMapper.get(id);
    }

    @Override
    public List<TaskVO> maplist(Map<String, Object> map) {
        return taskScheduleJobMapper.maplist(map);
    }

    @Override
    public int count(Map<String, Object> map) {
        return taskScheduleJobMapper.count(map);
    }

    @Override
    public int save(QuartzDO taskScheduleJob) {
        taskScheduleJob.setCreateByName(BootAppUtil.getLoginKey().getOperatorName());
        taskScheduleJob.setCreateBy(BootAppUtil.getLoginKey().getPersonCode());
        taskScheduleJob.setCreateDate(new Date());
        int ret = taskScheduleJobMapper.insert(taskScheduleJob);
        ScheduleJob job = ScheduleJobUtils.entityToData(taskScheduleJob);
        // 有效的作业，要创建一个Job，无效的作业不需要添加到作业任务中
        if ("1".equals(taskScheduleJob.getIsConcurrent())) {
            quartzManager.addJob(job);
        }
        return ret;
    }

    @Override
    public int update(QuartzDO taskScheduleJob) {
        taskScheduleJob.setUpdateBy(BootAppUtil.getLoginKey().getPersonCode());
        taskScheduleJob.setUpdateByName(BootAppUtil.getLoginKey().getOperatorName());
        taskScheduleJob.setUpdateDate(new Date());
        int ret = taskScheduleJobMapper.update(taskScheduleJob);

        // 更新作业调度
        try {
            ScheduleJob job = ScheduleJobUtils.entityToData(taskScheduleJob);
            if ("1".equals(taskScheduleJob.getIsConcurrent())) {
                // 有效的作业，要更新或者创建一个Job
                if (quartzManager.existsJob(job)) {
                    quartzManager.updateJobCron(job);
                } else {
                    quartzManager.addJob(job);
                }
            } else {
                // 无效的作业要删除Job
                if (quartzManager.existsJob(job)) {
                    quartzManager.deleteJob(job);
                }
            }
        } catch (SchedulerException e) {
            String message = i18nUtil.getMessage("ERR.quartz.jobService.updateFail");
            throw new QmException(message, e);
        }
        return ret;
    }

    @Override
    public int running(String id) {
        try {
            QuartzDO scheduleJob = quartzMapper.selectById(id);
            if (!"1".equals(scheduleJob.getIsConcurrent())) {
                scheduleJob.setIsConcurrent("1");
                quartzMapper.updateById(scheduleJob);
            }
            quartzManager.runAJobNow(ScheduleJobUtils.entityToData(scheduleJob));
            return 1;
        } catch (SchedulerException e) {
            log.info("---error--执行Job[" + id + "]失败！", e);
            return 0;
        }
    }

    @Override
    public int count() {
        return taskScheduleJobMapper.count(new HashMap<>());
    }

    @Override
    public int remove(String id) {
        try {
            QuartzDO scheduleJob = quartzMapper.selectById(id);
            quartzManager.deleteJob(ScheduleJobUtils.entityToData(scheduleJob));
            return taskScheduleJobMapper.remove(id);
        } catch (SchedulerException e) {
            log.info("---error--删除Job[" + id + "]失败！", e);
            return 0;
        }
    }

    @Override
    public int batchRemove(String[] ids) {
        for (String id : ids) {
            try {
                TaskVO scheduleJob = get(id);
                quartzManager.deleteJob(ScheduleJobUtils.entityToVoData(scheduleJob));
            } catch (SchedulerException e) {
                log.info("---error--删除Job[" + id + "]失败！", e);
                return 0;
            }
        }
        return taskScheduleJobMapper.batchRemove(ids);
    }

    @Override
    public void initSchedule() throws SchedulerException {
        // 这里获取任务信息数据
        List<TaskVO> jobList = taskScheduleJobMapper.maplist(new HashMap<String, Object>(16));
        for (TaskVO scheduleJob : jobList) {
            if ("1".equals(scheduleJob.getIsConcurrent())) {
                ScheduleJob job = ScheduleJobUtils.entityToVoData(scheduleJob);
                quartzManager.addJob(job);
            }
        }
    }

    @Override
    public void changeStatus(String jobId, String cmd) throws SchedulerException {
        QuartzDO scheduleJob = quartzMapper.selectById(jobId);
        if (scheduleJob == null) {
            return;
        }
        if (Constant.STATUS_RUNNING_STOP.equals(cmd)) {
            quartzManager.deleteJob(ScheduleJobUtils.entityToData(scheduleJob));
            scheduleJob.setJobStatus(ScheduleJob.STATUS_NOT_RUNNING);
        } else {
            if (Constant.STATUS_RUNNING_START.equals(cmd)) {
                scheduleJob.setJobStatus(ScheduleJob.STATUS_RUNNING);
                quartzManager.addJob(ScheduleJobUtils.entityToData(scheduleJob));
            }
        }
        update(scheduleJob);
    }

    @Override
    public void updateCron(String jobId) throws SchedulerException {
        QuartzDO scheduleJob = quartzMapper.selectById(jobId);
        if (scheduleJob == null) {
            return;
        }
        if (ScheduleJob.STATUS_RUNNING.equals(scheduleJob.getJobStatus())) {
            quartzManager.updateJobCron(ScheduleJobUtils.entityToData(scheduleJob));
        }
        update(scheduleJob);
    }

    @Override
    public int saveJobLog(QuartzLogDO jobLogDo) {
        return taskScheduleJobMapper.saveJobLog(jobLogDo);
    }

    @Transactional(rollbackFor = Exception.class, propagation = REQUIRES_NEW)
    @Override
    public void updateJobLog(QuartzLogDO jobLogDo) {
        taskScheduleJobMapper.updateJobLog(jobLogDo);
    }

    @Override
    public List<String> remoteGroupList() {
        return taskScheduleJobMapper.remoteGroupList();
    }

    @Override
    public List<String> remoteTaskListByGroup(String vGroup) {
        return taskScheduleJobMapper.remoteTaskListByGroup(vGroup);
    }
}
