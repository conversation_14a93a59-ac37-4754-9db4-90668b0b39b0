package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.WmsInterfaceRemote;
import com.qm.ep.quartz.service.WmsInterfaceService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WmsInterfaceServiceImpl implements WmsInterfaceService {

    @Autowired
    private WmsInterfaceRemote feignRemote;

    @Override
    public JsonResultVo spaMasterDocumentIssued() {
        return  feignRemote.spaMasterDocumentIssued("15","IF-BS-WMS-SPA-ZWJ-001");
    }

    @Override
    public JsonResultVo receiptIssuedInterface() {
        return feignRemote.receiptIssuedInterface("15","IF-BS-WMS-SPA-SHD-001");
    }

    @Override
    public JsonResultVo spaBillOfLadingIssued() {
        return feignRemote.spaBillOfLadingIssued("15","IF-BS-WMS-SPA-IN-002");
    }

    @Override
    public JsonResultVo compareInventory() {
        return feignRemote.compareInventory("15");
    }

    @Override
    public JsonResultVo inventoryChange() {
        return feignRemote.inventoryChange("15");
    }

    @Override
    public JsonResultVo syncBoutiqueData() {
        return feignRemote.syncBoutiqueData("15");
    }
}
