package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class NewxbcxsqdFallbackFactory implements FallbackFactory<NewxbcxsqdRemote> {

    @Override
    public NewxbcxsqdRemote create(Throwable throwable) {
        NewxbcxsqdFeignRemoteHystrix newxbcxsqdFeignRemoteHystrix = new NewxbcxsqdFeignRemoteHystrix();
        newxbcxsqdFeignRemoteHystrix.setHystrixEx(throwable);
        return newxbcxsqdFeignRemoteHystrix;
    }
}
