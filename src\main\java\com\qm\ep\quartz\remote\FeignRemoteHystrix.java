package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class FeignRemoteHystrix extends QmRemoteHystrix<FeignRemote> implements FeignRemote {

    @Override
    public JsonResultVo<Object> review(String tenantId) {
        return getResult();
    }
}
