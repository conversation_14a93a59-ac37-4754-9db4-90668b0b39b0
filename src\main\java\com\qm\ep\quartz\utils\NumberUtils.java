package com.qm.ep.quartz.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/8
 */
public class NumberUtils {


    /**
     * 将整数转换为负数
     * @param number
     * @return
     */
    public static Integer conversionNegativeVal(Integer number){
        return ~ (number - 1);
    }

    /**
     * 转换为正值
     * @param number
     * @return
     */
    public static Integer conversionPositiveValue(Integer number){
        return ~number + 1;
    }
}
