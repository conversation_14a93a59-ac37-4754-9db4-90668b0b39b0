package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface SaltransationVoidService {

    /**
     * @Description: 交易自动作废
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> transationVoid();

    /**
     * @Description: 周订单周末释放
     * @Param: [day]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> weekRelease(String day);

    /**
     * @Description: 月计划高排接口
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> erpSaljhpcjg();

    /**
     * @Description: 月计划订单计划接口
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> erpSalDdjh();

    /**
     * @Description: 订单中心——订单入库信息
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendDDRKToERP(String addRess);

    /**
     * @Description: 订单中心——发运出库信息
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendFYCKToERP(String addRess);

    /**
     * @Description: 订单中心——经销商入库信息
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendJXSRKToERP(String addRess);

    /**
     * @Description: 订单中心——客户交付信息
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendKHJFToERP(String addRess);

    /**
     * @Description: 订单中心——人工日历信息
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendRGRLToERP(String addRess);

    /**
     * @Description: 销售计划——同步电商中心FBOM
     * @Param: [addRess]
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> syncVehicleFeature(String addRess);

    /**
     * @Description: 同步DMS订单
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> syncDmsOrder();

    /**
     * @Description: 同步进出口计划日历
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendPlanCalerderToJCK();

    /**
     * 给OTD同步经销商主数据
     *
     * @return
     */
    JsonResultVo<Boolean> sendDealerInfoToOTD();

    /**
     * @Description: 定时任务-匹配排产明细数据
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> sendMatchPorduct();

    /**
     * @Description: 定时任务-AB类订单明细释放
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 刘伟新
     * @Date: 2022/7/1
     */
    JsonResultVo<Object> weekEditRelease();

    /**
     * @Description: 同步DMS销售订单上传记录
     * @Param: []
     * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
     * @Author: 许可
     * @Date: 2022/12/10
     */
    JsonResultVo<Object> syncDmsOrdersUploadLog();

    /**
    * @Description: 下发客户订单进度变动信息
    * @Param: []
    * @return: com.qm.tds.api.domain.JsonResultVo<java.lang.Object>
    * @Author: 刘伟新
    * @Date: 2023/4/14
    */
    JsonResultVo<Object> custOrderProcessChang();


}
