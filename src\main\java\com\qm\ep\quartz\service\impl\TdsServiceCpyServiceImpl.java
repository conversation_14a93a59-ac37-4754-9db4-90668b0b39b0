package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.ActQuotaFeignRemote;
import com.qm.ep.quartz.remote.TdsServiceCpyRemote;
import com.qm.ep.quartz.service.ActQuotaService;
import com.qm.ep.quartz.service.TdsServiceCpyService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * ClassName:ActQuotaServiceImpl
 * package:com.qm.ep.quartz.service.impl
 * Description:
 *
 * @Date:2021/6/15 14:06
 * @Author:sunyu
 */

@Slf4j
@Service
public class TdsServiceCpyServiceImpl implements TdsServiceCpyService {
    @Autowired
    private TdsServiceCpyRemote tdsServiceCpyRemote;

    @Override
    public JsonResultVo sendBankMessage(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage1(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage2(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage3(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage4(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage5(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage6(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage7(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage8(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage10(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage11(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage12(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage13(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage14(String bankCode) {
        return tdsServiceCpyRemote.sendBankMessage(bankCode);
    }

    @Override
    public JsonResultVo sendBankMessage80(String bankCode, String tranfunc) {
        return tdsServiceCpyRemote.sendBankMessage80(bankCode,tranfunc);
    }

    @Override
    public JsonResultVo pushCd() {
        return tdsServiceCpyRemote.pushCd();
    }

    @Override
    public JsonResultVo pushF1f2() {
        return tdsServiceCpyRemote.pushF1f2();
    }
}
