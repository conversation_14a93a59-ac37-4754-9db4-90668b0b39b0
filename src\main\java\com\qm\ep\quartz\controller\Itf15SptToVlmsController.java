package com.qm.ep.quartz.controller;

import com.qm.ep.quartz.service.Itf15SptToVlmsService;
import com.qm.tds.api.controller.BaseController;
import com.qm.tds.api.domain.JsonResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: jianghong
 * @time: 2021/4/2 10:45
 */
@Tag(name = "调试定时任务使用", description = "调试定时任务使用")
@RestController
@RequestMapping("/itf15SptToVlms")
public class Itf15SptToVlmsController extends BaseController {

    @Autowired
    Itf15SptToVlmsService itf15SptToVlmsService;

    @Operation(summary = "省 001", description = "SptToVlms：省 001[author:10027705]")
    @PostMapping("/transferDataOfcsqdm")
    public JsonResultVo transferDataOfcsqdm(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfcsqdm(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：市 002
     */
    @Operation(summary = "市 002", description = "SptToVlms：市 002[author:10027705]")
    @PostMapping("/transferDataOfcsxdm")
    public JsonResultVo transferDataOfcsxdm(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfcsxdm(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：区县 003
     */
    @Operation(summary = "区县 003", description = "SptToVlms：区县 003[author:10027705]")
    @PostMapping("/transferDataOfcxqdm")
    public JsonResultVo transferDataOfcxqdm(@RequestBody String loginKey) {

        return itf15SptToVlmsService.transferDataOfcxqdm(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：产品代码 004
     */
    @Operation(summary = "产品代码 004", description = "SptToVlms：产品代码 004[author:10027705]")
    @PostMapping("/transferDataOfcpdm")
    public JsonResultVo transferDataOfcpdm(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfcpdm(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：发运计划 005
     */
    @Operation(summary = "发运计划 005", description = "SptToVlms：发运计划 005[author:10027705]")
    @PostMapping("/transferDataOffyjh")
    public JsonResultVo transferDataOffyjh(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOffyjh(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：删除发运计划 006
     */
    @Operation(summary = "删除发运计划 006", description = "SptToVlms：删除发运计划 006[author:10027705]")
    @PostMapping("/transferDataOftfyjh")
    public JsonResultVo transferDataOftfyjh(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOftfyjh(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：指派车道 007
     */
    @Operation(summary = "指派车道 007", description = "SptToVlms：指派车道 007[author:10027705]")
    @PostMapping("/transferDataOfzpcd")
    public JsonResultVo transferDataOfzpcd(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfzpcd(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：取消指派车道 008
     */
    @Operation(summary = "取消指派车道 008", description = "SptToVlms：取消指派车道 008[author:10027705]")
    @PostMapping("/transferDataOfqxzpcd")
    public JsonResultVo transferDataOfqxzpcd(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfqxzpcd(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：发运出库 009
     */
    @Operation(summary = "发运出库 009", description = "SptToVlms：发运出库 009[author:10027705]")
    @PostMapping("/transferDataOffyck")
    public JsonResultVo transferDataOffyck(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOffyck(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：出库换车 010
     */
    @Operation(summary = "出库换车 010", description = "SptToVlms：出库换车 010[author:10027705]")
    @PostMapping("/transferDataOfckhc")
    public JsonResultVo transferDataOfckhc(@RequestBody String loginKey) {

        return itf15SptToVlmsService.transferDataOfckhc(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：车辆到货 011
     */
    @Operation(summary = "车辆到货 011", description = "SptToVlms：车辆到货 011[author:10027705]")
    @PostMapping("/transferDataOfcldh")
    public JsonResultVo transferDataOfcldh(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfcldh(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：计划返单 012
     */
    @Operation(summary = "计划返单 012", description = "SptToVlms：计划返单 012[author:10027705]")
    @PostMapping("/transferDataOfjhfd")
    public JsonResultVo transferDataOfjhfd(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfjhfd(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：运费结算 013
     */
    @Operation(summary = "运费结算 013", description = "SptToVlms：运费结算 013[author:10027705]")
    @PostMapping("/transferDataOfyfjs")
    public JsonResultVo transferDataOfyfjs(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfyfjs(loginKey);
    }

    /**
     * 定时任务 - SptToVlms：下线入库信息传给一汽物流接口
     */
    @Operation(summary = "下线入库信息传给一汽物流接口", description = "SptToVlms：下线入库信息传给一汽物流接口[author:10027705]")
    @PostMapping("/transferDataOfWms")
    public JsonResultVo transferDataOfWms(@RequestBody String loginKey) {
        return itf15SptToVlmsService.transferDataOfWms(loginKey);
    }
}
