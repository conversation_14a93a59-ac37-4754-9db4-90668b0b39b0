package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ClassName:ActQuotaRemote
 * package:com.qm.ep.quartz.remote
 * Description:
 *
 * @Date:2021/6/15 13:22
 * @Author:sunyu
 */

@Repository
@FeignClient(name = "tds-service-sal", fallbackFactory =ActQuotaFeignFallbackFactory.class)
public interface ActQuotaFeignRemote {

    /**
     * 活动额度测算
     */
    @PostMapping("/salc322/actQuota")
    JsonResultVo actQuota(@RequestHeader("tenantId") String tenantId, @RequestParam("year") String year, @RequestParam("month") String month);

}
