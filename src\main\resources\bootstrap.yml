server:
  port: 7009
spring:
  application:
    name: common-service-quartz
  profiles:
    active: nacos
--- #"---"用于分隔不同的profiles()文档块
spring:
  profiles:
    on-profile: dev
  cloud:
    config:
      uri: http://localhost:7005/
      fail-fast: true
      name: ${spring.application.name}
      profile: ${spring.profiles.active}
--- #"---"用于分隔不同的profiles()文档块
spring:
  profiles:
    on-profile: nacos
  # 配置中心
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        namespace: ep-hq
        username : nacos
        password : nacos
        group: TDS_EP_NACOS_GROUP
        file-extension: yaml
        shared-configs[0]:
          data-id: tds-global-config.yml
          group: TDS_EP_NACOS_GROUP
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true