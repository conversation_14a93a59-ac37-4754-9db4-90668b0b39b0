package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.qm.ep.quartz.remote.Itf15SptToMpsRemote;
import com.qm.ep.quartz.service.Itf15SptToMpsService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.base.domain.LoginKeyDO;
import com.qm.tds.util.BootAppUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SptToErp 接口 (下线入库,出库)
 *
 * @author: jianghong
 * @time: 2021/4/12 18:02
 */
@Service
public class Itf15SptToMpsServiceImpl implements Itf15SptToMpsService {

    @Autowired
    Itf15SptToMpsRemote itf15SptToMpsRemote;

    @Override
    public JsonResultVo<Object> transferDataOfMps001(String loginKey) {
        LoginKeyDO loginKeyDO = getLoginKey(loginKey);
        return itf15SptToMpsRemote.transferDataOfMps001(loginKeyDO.getCompanyId(),
                loginKeyDO.getCustGroupid(),
                loginKeyDO.getTenantId(),
                loginKeyDO.getOperatorId());
    }

    //获取loginkey
    private LoginKeyDO getLoginKey(String loginKey) {
        LoginKeyDO loginKeyDO;
        if (BootAppUtil.isnotNullOrEmpty(loginKey)) loginKeyDO = JSON.parseObject(loginKey, LoginKeyDO.class);
        else {
            loginKeyDO = new LoginKeyDO();
            loginKeyDO.setCompanyId("6000");
            loginKeyDO.setCustGroupid("15");
            loginKeyDO.setTenantId("15");
            loginKeyDO.setOperatorId("*********");
        }
        return loginKeyDO;
    }
}
