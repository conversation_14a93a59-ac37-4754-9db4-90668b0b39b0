package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 二手车公共
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "uc-service-isc", fallbackFactory = FeignFallbackFactory.class)
public interface UcmsIscFeignRemote {

    /**
     * 第一车网品牌数据接口（商用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarRootSY")
    JsonResultVo getIautosCarRootSY(@RequestHeader("tenantId") String tenantId);

    /**
     * 第一车网厂商车系数据接口（商用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarBrandSeriesSY")
    JsonResultVo getIautosCarBrandSeriesSY(@RequestHeader("tenantId") String tenantId);

    /**
     * 第一车网车型数据接口（商用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarTypeSY")
    JsonResultVo getIautosCarTypeSY(@RequestHeader("tenantId") String tenantId);

    /**
     * 第一车网品牌数据接口（乘用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarRootCY")
    JsonResultVo getIautosCarRootCY(@RequestHeader("tenantId") String tenantId);

    /**
     * 第一车网厂商车系数据接口（乘用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarBrandSeriesCY")
    JsonResultVo getIautosCarBrandSeriesCY(@RequestHeader("tenantId") String tenantId);

    /**
     * 第一车网车型数据接口（乘用车）
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getIautosCarTypeCY")
    JsonResultVo getIautosCarTypeCY(@RequestHeader("tenantId") String tenantId);

    /**
     * 获取旧车厂商
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getOCarBrand")
    JsonResultVo getOCarBrand(@RequestHeader("tenantId") String tenantId);

    /**
     * 获取旧车车系
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getOCarSeries")
    JsonResultVo getOCarSeries(@RequestHeader("tenantId") String tenantId);

    /**
     * 获取旧车车型
     * add by zhoul
     */
    @PostMapping("/iautosUCMS/getOCarType")
    JsonResultVo getOCarType(@RequestHeader("tenantId") String tenantId);

}
