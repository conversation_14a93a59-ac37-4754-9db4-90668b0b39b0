package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Repository
 @FeignClient(name = "tds-service-svc-statistics", fallbackFactory = StatisticsFeignFallbackFactory.class)
public interface StatisticsFeignRemote {

    /**
     * 定时任务
     */
    @GetMapping("/dealerBusinessData/sync")
    JsonResultVo syncDealerBusinessData(@RequestHeader("tenantId") String tenantId, @RequestParam Map param);

}
