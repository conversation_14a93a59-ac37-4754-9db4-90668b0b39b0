package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

/**
 * @author: jianghong
 * @time: 2021/4/1 13:45
 */
public interface Itf15VlmsToSptService {
    /**
     * 定时任务 - VlmsToSpt：司机信息 001
     */
    JsonResultVo transferDataOfsjxx(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：运输车信息 002
     */
    JsonResultVo transferDataOfyscxx(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003
     */
    JsonResultVo transferDataOfzpyss(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003 (月底之前执行,因为定时任务名字不能重复,一样的任务起两个名字才能建两个定时任务)
     */
    JsonResultVo transferDataOfzpyss2(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：指派运输商 003 (3\4状态补传)
     */
    JsonResultVo transferDataOfzpyss3(String loginKey);
    /**
     * 定时任务 - VlmsToSpt：取消指派运输商 004
     */
    JsonResultVo transferDataOfqxzpyss(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：变更运输商 005
     */
    JsonResultVo transferDataOfbgyss(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：打印提车单 006
     */
    JsonResultVo transferDataOfdytcd(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：车辆位置信息 007
     */
    JsonResultVo transferDataOfgpszt(String loginKey);

    /**
     * 定时任务 - VlmsToSpt：在途时间点 008
     */
    JsonResultVo transferDataOfsjztsj(String loginKey);

}
