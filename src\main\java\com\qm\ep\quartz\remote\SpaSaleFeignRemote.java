package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


@Repository
 @FeignClient(name = "tds-service-spa-manapur", fallbackFactory = SpaSaleFeignFallbackFactory.class)
public interface SpaSaleFeignRemote {

    /**
     * 定时任务执行 采购合同终审
     */
    @PostMapping("/spaPurchasingMain/AutoFormSaleOrder")
    JsonResultVo AutoFormSaleOrder(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务维护收货通知单 申请作废 超过24小时短信发送
     */
    @PostMapping("/spaPurchasingMiddle/sendSMSByTwentyFourHours")
    JsonResultVo sendSMSByTwentyFourHours(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务维护收货通知单 申请作废 超过４８小时短信发送
     */
    @PostMapping("/spaPurchasingMiddle/sendSMSByFortyEightHours")
    JsonResultVo sendSMSByFortyEightHours(@RequestHeader("tenantId") String tenantId);

    /**
     *  新采购算法，定时加工 nbo  删除临时表 spawnbo1 和 spawnbo2
     */
    @PostMapping("/spaPurchasingMiddle/newAlgorithmCalculateNbo")
    JsonResultVo newAlgorithmCalculateNbo(@RequestHeader("tenantId") String tenantId);
}
