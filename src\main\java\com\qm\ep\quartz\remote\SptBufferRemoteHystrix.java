package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - Spt 缓存直发车辆自动锁车功能
 */
@Component
@Slf4j
public class SptBufferRemoteHystrix extends QmRemoteHystrix<SptBufferRemote> implements SptBufferRemote {

    @Override
    public JsonResultVo<Boolean> autoLock(String companyId, String custGroupId, String tenantId, String userId) {
        return this.getResult();
    }

    @Override
    public JsonResultVo<Boolean> otdOrderMatchVin(String companyId, String custGroupId, String tenantId, String userId) {
        return this.getResult();
    }
}
