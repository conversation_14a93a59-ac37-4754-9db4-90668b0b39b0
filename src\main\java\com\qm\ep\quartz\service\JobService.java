package com.qm.ep.quartz.service;

import com.qm.ep.quartz.domain.bean.QuartzDO;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.domain.vo.TaskVO;
import org.quartz.SchedulerException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-09-26 20:53:48
 */
public interface JobService {

    TaskVO get(String id);

    List<TaskVO> maplist(Map<String, Object> map);

    int count(Map<String, Object> map);

    int save(QuartzDO taskScheduleJob);

    int update(QuartzDO taskScheduleJob);

    int count();

    int remove(String id);

    int running(String id);

    int batchRemove(String[] ids);

    void initSchedule() throws SchedulerException;

    void changeStatus(String jobId, String cmd) throws SchedulerException;

    void updateCron(String jobId) throws SchedulerException;


    int saveJobLog(QuartzLogDO jobLogDo);


    void updateJobLog(QuartzLogDO jobLogDo);

    List<String> remoteGroupList();

    List<String> remoteTaskListByGroup(String vGroup);
}
