package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SvcQAFeedbackBillFeignRemote;
import com.qm.ep.quartz.service.QuartzLogService;
import com.qm.ep.quartz.service.SvcWorkPriceTimeIssuedService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SvcWorkPriceTimeIssuedServiceImpl implements SvcWorkPriceTimeIssuedService {
    @Autowired
    private SvcQAFeedbackBillFeignRemote feignRemote;

    @Autowired
    private QuartzLogService quartzLogService;

    @Override
    public JsonResultVo<Object> workPriceTimeIssuedForDms() {
        String vLastDtstamp = quartzLogService.selectMaxJobDtstamp("com.qm.ep.quartz.service.SvcWorkPriceTimeIssuedService:workPriceTimeIssuedForDms%");
        if (BootAppUtil.isNullOrEmpty(vLastDtstamp)) {
            vLastDtstamp = "1900-01-01";
        }
        log.debug("vLastDtstamp:" + vLastDtstamp);
        /**
         * 因为工时单价定时下发取不到公司id所以 现在写死公司id 红旗的默认是6000
         * 这个是测试用的，发布正式要去掉
         * vLastDtstamp = "2020-01-01";
         */
        return feignRemote.workPriceTimeIssuedForDms("15", "6000", vLastDtstamp);
    }
}

