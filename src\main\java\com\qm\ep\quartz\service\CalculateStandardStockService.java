package com.qm.ep.quartz.service;

import com.qm.tds.api.domain.JsonResultVo;

public interface CalculateStandardStockService {
    // 计算季节件和非季节件
    JsonResultVo startScheduleBySSQ();

    // 自动分货 正常订单
    JsonResultVo autoDistribNormal();

    // 自动分货 紧急订单
    JsonResultVo autoDistribUrgent();

    // 自动分货 文创订单
    JsonResultVo autoDistribCulturalCreation();

    // 计算spar925 库存销售日报表
    JsonResultVo calculateSpar925();

    // 超期欠货订单作废
    JsonResultVo autoabolish();

    // 经销商在库品种
    JsonResultVo autoqueryrate();

    JsonResultVo summary();

    // 续保
    JsonResultVo autoRenewRatio(String params);

    // 大咖支援
    JsonResultVo autoServiceSupport(String params);

    // 一次性修复率
    JsonResultVo autoFFVComplaint(String params);

    // aak
    JsonResultVo<Object> getAak(String params);

    JsonResultVo<Object> getRecording(String params);

    // 自动分货 --新
    JsonResultVo autoDistribCulturalNew();
    JsonResultVo autoDistribCulturalNew2();
    JsonResultVo autoDistribCulturalNew22();
}
