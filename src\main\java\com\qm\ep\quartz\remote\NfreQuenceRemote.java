package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;


/**
 * 批量更新索赔频次
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "sc-service-spa", fallbackFactory = NfreQuenceFallbackFactory.class)
public interface NfreQuenceRemote {

    /**
     * 定时任务 - 更新索赔频次
     */
    @PostMapping("/spaClaimMsg/nfreQuence")
    JsonResultVo nfreQuence(@RequestHeader("tenantId") String tenantId);

}
