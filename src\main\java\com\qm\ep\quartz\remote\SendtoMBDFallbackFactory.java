package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class SendtoMBDFallbackFactory implements FallbackFactory<SendtoMBDRemote> {

    @Override
    public SendtoMBDRemote create(Throwable throwable) {
        SendtoMBDRemoteHystrix sendtoMBDRemoteHystrix = new SendtoMBDRemoteHystrix();
        sendtoMBDRemoteHystrix.setHystrixEx(throwable);
        return sendtoMBDRemoteHystrix;
    }
}
