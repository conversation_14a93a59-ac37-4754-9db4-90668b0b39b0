package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.service.sendDingMessage2Service;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class sendDingMessage2Impl implements sendDingMessage2Service {

    @Autowired
    private CustomRemote customRemote;

    @Override
    public JsonResultVo sendDingMessage2() {
        return customRemote.sendDingMessage2("15", "6000");
    }
}
