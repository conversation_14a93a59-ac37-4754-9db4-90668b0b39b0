package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class UTFinFeignFallbackFactory implements FallbackFactory<UTFinFeignRemote> {

    @Override
    public UTFinFeignRemote create(Throwable throwable) {
        UTFinFeignRemoteHystrix feignRemoteHystrix = new UTFinFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
