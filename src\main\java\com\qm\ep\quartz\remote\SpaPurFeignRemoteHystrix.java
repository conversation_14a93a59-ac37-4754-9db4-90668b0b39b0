package com.qm.ep.quartz.remote;


import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class SpaPurFeignRemoteHystrix extends QmRemoteHystrix<SpaPurFeignRemote> implements SpaPurFeignRemote {
    @Override
    public JsonResultVo<Object> finalJudgmeByDate(String tenantId) {
        return getResult();
    }

}
