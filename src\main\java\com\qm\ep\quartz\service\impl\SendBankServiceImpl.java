package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.SendBankRemote;
import com.qm.ep.quartz.remote.SendSALB061ToEPRemote;
import com.qm.ep.quartz.service.SendBankService;
import com.qm.ep.quartz.service.SendSALB061ToEPService;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Service
public class SendBankServiceImpl implements SendBankService {
    @Autowired
    private SendBankRemote sendBankRemote;

    @Override
    public JsonResultVo sendBankPingAn() {
        return sendBankRemote.sendBankPingAn("15");
    }
}
