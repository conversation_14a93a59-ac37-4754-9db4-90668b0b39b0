package com.qm.ep.quartz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qm.ep.quartz.domain.bean.QuartzLogDO;
import com.qm.ep.quartz.mapper.QuartzLogMapper;
import com.qm.ep.quartz.remote.CustomRemote;
import com.qm.ep.quartz.remote.SvcMntnFeignRemote;
import com.qm.ep.quartz.service.JobCustomService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.DateUtils;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class JobCustomServiceImpl implements JobCustomService {

    @Autowired
    private CustomRemote customRemote;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private QuartzLogMapper quartzLogMapper;
    @Autowired
    private SvcMntnFeignRemote svcMntnFeignRemote;

    @Override
    public JsonResultVo autVehicleService() {
        String time = quartzLogMapper.getVehicleLastTime("1");
        if (BootAppUtil.isNullOrEmpty(time)) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.JobCustomServiceImpl.autVehicleService");
            throw new QmException(errorMsg);
        }
        Map<String, String> timeM = new HashMap<>();
        timeM.put("time", time);
        return customRemote.autVehicle("15", timeM);
    }

    @Override
    public JsonResultVo autVehicleService2(String timesStr) {

        QuartzLogDO times = JSONObject.parseObject(timesStr, QuartzLogDO.class);
        String time = "";
        if (BootAppUtil.isNullOrEmpty(times.getJobStartTime())) {
            time = quartzLogMapper.getVehicleLastTime("2");
        } else {
            time = times.getJobStartTime();
        }
        if (BootAppUtil.isNullOrEmpty(time)) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.JobCustomServiceImpl.autVehicleService");
            throw new QmException(errorMsg);
        }
        Map<String, String> timeM = new HashMap<>();
        timeM.put("time", time);
        timeM.put("time2", times.getJobEndTime());
        return customRemote.autVehicle2("15", timeM);
    }

    @Override
    public JsonResultVo autVehicleService3(String timesStr) {
        QuartzLogDO times = JSON.parseObject(timesStr, QuartzLogDO.class);
        String time = "";
        if (BootAppUtil.isNullOrEmpty(times.getJobStartTime())) {
            time = quartzLogMapper.getVehicleLastTime("3");
        } else {
            time = times.getJobStartTime();
        }
        if (BootAppUtil.isNullOrEmpty(time)) {
            String errorMsg = i18nUtil.getMessage("ERR.quartz.JobCustomServiceImpl.autVehicleService");
            throw new QmException(errorMsg);
        }
        Map<String, String> timeM = new HashMap<>();
        timeM.put("time", time);
        timeM.put("time2", times.getJobEndTime());
        return customRemote.autVehicle3("15", timeM);
    }

    @Override
    public JsonResultVo saveMdac008d1(String timesStr) {
        QuartzLogDO times = JSON.parseObject(timesStr, QuartzLogDO.class);
        String time = "";
        String time2 = "";
        if (BootAppUtil.isNullOrEmpty(times.getJobStartTime())) {
            time = quartzLogMapper.getMdac008d1Update();
        } else {
            time = times.getJobStartTime();
        }
        if (BootAppUtil.isNullOrEmpty(times.getJobEndTime())) {
            time2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(DateUtils.getSysdateTime());
            ;
        } else {
            time2 = times.getJobEndTime();
        }
        Map<String, String> timeM = new HashMap<>();
        timeM.put("time", time);
        timeM.put("time2", time2);
        return customRemote.SvcMdac008d1("15", timeM);
    }

    @Override
    public JsonResultVo dealMntnRecordData() {
        return svcMntnFeignRemote.dealMntnRecordData("15");
    }

    @Override
    public JsonResultVo repairMntnRecord() {
        return svcMntnFeignRemote.repairMntnRecord("15");
    }

    /**
     * 同步客户车辆档案到新能源
     *
     * @return
     */
    @Override
    public JsonResultVo sendCustomerToXny() {
        return customRemote.sendCustomerToXny("15", "6000");
    }

    @Override
    public JsonResultVo sendCustomerToV2() {
        return customRemote.sendCustomerToV2("15", "6000");
    }


    @Override
    public JsonResultVo<Boolean> getSMRGBC(){
        return customRemote.getSMRGBC("15");
    }
}
