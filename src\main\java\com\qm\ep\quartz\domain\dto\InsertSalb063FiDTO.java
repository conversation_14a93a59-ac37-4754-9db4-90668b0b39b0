package com.qm.ep.quartz.domain.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "插入 salb063 fi DTO")
@Data
public class InsertSalb063FiDTO {

    @Schema(description = "当前天")
    private String currentday;

    @Schema(description = "当月")
    private String currentmonth;

    @Schema(description = "上个月")
    private String lastmonth;

    @Schema(description = "数据lastlastmonth")
    private String lastlastmonth;



}
