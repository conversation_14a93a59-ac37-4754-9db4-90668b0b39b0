package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class UcmsRetFeignFallbackFactory implements FallbackFactory<UcmsRetFeignRemote> {

    @Override
    public UcmsRetFeignRemote create(Throwable throwable) {
        UcmsRetFeignRemoteHystrix feignRemoteHystrix = new UcmsRetFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
