package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时任务 - 自动生成价格
 */
@Component
@Slf4j
public class PriceRemoteHystrix extends QmRemoteHystrix<PriceRemote> implements PriceRemote {
    @Override
    public JsonResultVo<Object> generate(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> calculatefrequency(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> calculatefbfrequency(String tenantId) {
        return getResult();
    }

    @Override
    public JsonResultVo<Object> sysDealerInfo(String tenantId) {
        return getResult();
    }
}
