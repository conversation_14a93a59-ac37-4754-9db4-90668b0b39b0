package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;


/**
 * 服务批量结算定时任务 服务批量结算，供应商批量结算
 *
 * <AUTHOR>
 */
@Repository
@FeignClient(name = "tds-service-svc-bln", fallbackFactory = SvcBatchFeignFallbackFactory.class)
public interface SvcBatchFeignRemote {

    /**
     * 服务批量结算 服务批量结算，供应商批量结算
     */
    @PostMapping("/svcBlnTask/svcBatchTimingTask")
    JsonResultVo<Object> svcBatchTimingTask(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/vdrBlnTask/vdrBatchTimingTask")
    JsonResultVo<Object> vdrBatchTimingTask(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/svcInvc/autoSvcInvc")
    JsonResultVo<Object> autoSvcInvc(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/svcInvc/sendInvcBillInvcListAuto")
    JsonResultVo<Object> sendSvcInvc2FinV6(@RequestHeader("tenantId") String tenantId, @RequestBody Map<String, String> map);
}
