package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 形成销售返利
 * <AUTHOR>
 * @date 2021/7/9 10:34
 */
@Component
@Slf4j
public class AutoRebateFallbackFactory implements FallbackFactory<AutoRebateRemote> {
    @Override
    public AutoRebateRemote create(Throwable throwable) {
        AutoRebateRemoteHystrix autoRebateremotehystrix = new AutoRebateRemoteHystrix();
        autoRebateremotehystrix.setHystrixEx(throwable);
        return autoRebateremotehystrix;
    }
}
