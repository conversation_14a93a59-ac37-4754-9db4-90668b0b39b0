package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.TempnbykDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Data
public class SptQueryXxrkHystrix extends QmRemoteHystrix<SptQueryXxrkRemote> implements SptQueryXxrkRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Boolean> executeInWarehouse1(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse2(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse3(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse4(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse5(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse6(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse7(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse8(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse9(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse10(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse11(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse12(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse13(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse14(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> executeInWarehouse15(String tenantId, String companyId) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.common");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> internalMoveInTime(String tenantId, String companyId, TempnbykDTO tempnbykDTO) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.internalMoveInTime");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }

    @Override
    public JsonResultVo<Boolean> internalMoveOutTime(String tenantId, String companyId, TempnbykDTO tempnbykDTO) {
        String errorMsg = i18nUtil.getMessage("ERR.quartz.SptQueryXxrkHystrix.internalMoveOutTime");
        JsonResultVo<Boolean> resultVo = new JsonResultVo<>();
        resultVo.setCode(JsonResultVo.CODE_OK);
        resultVo.setMsgErr(errorMsg);
        return resultVo;
    }
}
