package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * 定时任务 -  wms接口调用
 */
@Repository
@FeignClient(name = "tds-service-spa-interface", fallbackFactory = WmsInterfaceFallbackFactory.class)
public interface WmsInterfaceRemote {

    /**
     * 定时任务 - wms物料主数据下发
     */
    @PostMapping("/wmsInterface/spaMasterDocumentIssued")
    JsonResultVo<Object> spaMasterDocumentIssued(@RequestHeader("tenantId") String tenantId,@RequestParam("vitfno") String vitfno);

    /**
     * 定时任务 - wms收货单下发数据下发
     */
    @PostMapping("/wmsInterface/receiptIssuedInterface")
    JsonResultVo<Object> receiptIssuedInterface(@RequestHeader("tenantId") String tenantId,@RequestParam("vitfno") String vitfno);

    /**
     * 定时任务 - wms提货单下发数据下发   IF-BS-WMS-SPA-IN-002
     */
    @PostMapping("/wmsInterface/spaBillOfLadingIssued")
    JsonResultVo<Object> spaBillOfLadingIssued(@RequestHeader("tenantId") String tenantId,@RequestParam("vitfno") String vitfno);

    /**
     * 定时任务 - wms库存对比  IIF-BS-WMS-SPA-IN-010
     */
    @PostMapping("/wmsInterface/compareInventory")
    JsonResultVo<Object> compareInventory(@RequestHeader("tenantId") String tenantId);

    /**
     * 定时任务 - wms库存对比  IIF-BS-WMS-SPA-IN-010
     */
    @PostMapping("/wmsInterface/inventoryChange")
    JsonResultVo<Object> inventoryChange(@RequestHeader("tenantId") String tenantId);

    /**
     * create by zhenghaibing on 2021-06-01
     * desc: 车型选配接口 2.2同步精品配件信息 OTD将数据推送给OTD
     * 定时任务： vitfno  IF-OTD-ORDER-SPA-OUT-001
     */
    @PostMapping("/wmsInterface/syncBoutiqueData")
    JsonResultVo<Object> syncBoutiqueData(@RequestHeader("tenantId") String tenantId);
}
