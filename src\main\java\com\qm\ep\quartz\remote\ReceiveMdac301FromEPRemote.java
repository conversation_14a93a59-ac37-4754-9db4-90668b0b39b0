package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@Repository
@FeignClient(name = "tds-service-fin", fallbackFactory = ReceiveMdac301FallbackFactory.class)
public interface ReceiveMdac301FromEPRemote {

    @PostMapping("/ifepmdac301c/receiveEPMdac301c")
    JsonResultVo<Boolean> receiveEPMdac301c(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/ifepmdac301c/receiveEPMdac300")
    JsonResultVo<Boolean> receiveEPMdac300(@RequestHeader("tenantId") String tenantId);

    @PostMapping("/ifepmdac301c/receiveEPMdac301d6")
    JsonResultVo<Boolean> receiveEPMdac301d6(@RequestHeader("tenantId") String tenantId);
}
