package com.qm.ep.quartz.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.qm.tds.api.domain.JsonParamDto;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-09
 */
@Data
@Schema(title = "定时任务入参", description = "定时任务入参")
public class QuartzDTO extends JsonParamDto implements Serializable {


    private static final long serialVersionUID = 4472346101200205229L;
    /**
     * id
     */
    @Schema(title = "id", description = "id")
    private String id;

    /**
     * cron表达式
     */
    @Schema(title = "cron表达式", description = "cron表达式")
    private String cronExpression;

    /**
     * 任务调用的方法名
     */
    @Schema(title = "任务调用的方法名", description = "任务调用的方法名")
    private String methodName;

    /**
     * 任务是否有状态
     */
    @Schema(title = "任务是否有状态", description = "任务是否有状态")
    private String isConcurrent;

    /**
     * 任务描述
     */
    @Schema(title = "任务描述", description = "任务描述")
    private String description;

    /**
     * 更新者
     */
    @Schema(title = "更新者", description = "更新者")
    private String updateBy;

    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    private String createByName;

    /**
     * 创建者
     */
    @Schema(title = "修改者", description = "修改者")
    private String updateByName;
    /**
     * 任务执行时调用哪个类的方法 包名+类名
     */
    @Schema(title = "任务执行时调用哪个类的方法 包名+类名", description = "任务执行时调用哪个类的方法 包名+类名")
    private String beanClass;

    /**
     * 创建时间
     */
    @Schema(title = "创建时间", description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 任务状态
     */
    @Schema(title = "任务状态", description = "任务状态")
    private String jobStatus;

    /**
     * 任务分组
     */
    @Schema(title = "任务分组", description = "任务分组")
    private String jobGroup;

    /**
     * 更新时间
     */
    @Schema(title = "更新时间", description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 创建者
     */
    @Schema(title = "创建者", description = "创建者")
    private String createBy;

    /**
     * Spring bean
     */
    @Schema(title = "Spring bean", description = "Spring bean")
    private String springBean;

    /**
     * 任务名
     */
    @Schema(title = "任务名", description = "任务名")
    private String jobName;

    @Schema(title = "重叠执行", description = "重叠执行")
    private String isParallel;

    @Schema(title = "任务参数", description = "任务参数")
    private String jobPara;

    /**
     * 时间戳
     */
    @Version
    @Schema(title = "时间戳", description = "时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(title = "开始时间", description = "开始时间")
    private String startTime;

    @Schema(title = "结束时间", description = "结束时间")
    private String endTime;

    @SuppressWarnings("squid:S1948")
    @Schema(title = "状态数组", description = "状态数组")
    private List statusList;

    /**
     * 预警标识
     */
    @Schema(title = "预警标识", description = "预警标识")
    private String warningFlag;
}
