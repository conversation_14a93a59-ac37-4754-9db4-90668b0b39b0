package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/24
 */
@Component
public class DataMonitorRemoteHystrix extends QmRemoteHystrix<DataMonitorRemote> implements DataMonitorRemote {

    @Override
    public JsonResultVo monitorDataByVgroup(String companyId,String tenantId, String vgroup) {
        return getResult();
    }
}
