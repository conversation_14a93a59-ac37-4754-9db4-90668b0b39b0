package com.qm.ep.quartz.remote;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName : FinanceRemoteHystrix
 * @Description :
 * <AUTHOR> WQS
 * @Date: 2021-03-27  9:58
 */
public class FinanceRemoteHystrix extends QmRemoteHystrix<FinanceRemote> implements FinanceRemote {
    @Autowired
    private I18nUtil i18nUtil;

    @Override
    public JsonResultVo<Object> finance(String tenantId) {
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        String errorMsg = i18nUtil.getMessage("ERR.quartz.FinanceRemoteHystrix.finance");
        resultVo.setMsgErr(errorMsg, getHystrixEx());
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> feignExeOut(String tenantId) {
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        String errorMsg = i18nUtil.getMessage("ERR.quartz.FinanceRemoteHystrix.feignExeOut");
        resultVo.setMsgErr(errorMsg, getHystrixEx());
        return resultVo;
    }

    @Override
    public JsonResultVo<Object> feignInvoice(String tenantId) {
        JsonResultVo<Object> resultVo = new JsonResultVo<>();
        String errorMsg = i18nUtil.getMessage("ERR.quartz.FinanceRemoteHystrix.feignInvoice");
        resultVo.setMsgErr(errorMsg, getHystrixEx());
        return resultVo;
    }
}
