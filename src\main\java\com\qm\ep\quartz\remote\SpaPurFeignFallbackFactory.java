package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class SpaPurFeignFallbackFactory implements FallbackFactory<SpaPurFeignRemote> {

    @Override
    public SpaPurFeignRemote create(Throwable throwable) {
        SpaPurFeignRemoteHystrix feignRemoteHystrix = new SpaPurFeignRemoteHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
