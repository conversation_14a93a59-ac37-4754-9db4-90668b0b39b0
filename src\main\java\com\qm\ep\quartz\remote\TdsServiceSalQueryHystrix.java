package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.dto.InsertSalb063FiDTO;
import com.qm.ep.quartz.domain.dto.SearchPriceDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;

public class TdsServiceSalQueryHystrix extends QmRemoteHystrix<TdsServiceSalQueryRemote> implements TdsServiceSalQueryRemote{
    @Override
    public JsonResultVo insertSalb063Fi(String tenantId, InsertSalb063FiDTO paramdDTO) {
        return getResult();
    }

    @Override
    public JsonResultVo tdsHistoryV2(String tenantId, SearchPriceDTO searchPriceDTO) {
        return getResult();
    }
}
