package com.qm.ep.quartz.remote;

import com.qm.ep.quartz.domain.bean.Mdac315CDO;
import com.qm.ep.quartz.domain.dto.Mdac315CDListDTO;
import com.qm.ep.quartz.domain.dto.Mdac315MiddleDTO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
public class SendBankRemoteHystrix extends QmRemoteHystrix<SendBankRemote> implements SendBankRemote {

    @Override
    public JsonResultVo sendBankPingAn(String tenantId) {
        return getResult();
    }


}
