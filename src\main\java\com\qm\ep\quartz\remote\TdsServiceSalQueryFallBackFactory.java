package com.qm.ep.quartz.remote;

import feign.hystrix.FallbackFactory;

public class TdsServiceSalQueryFallBackFactory implements FallbackFactory<TdsServiceSalQueryRemote> {
    @Override
    public TdsServiceSalQueryRemote create(Throwable throwable) {
        TdsServiceSalQueryHystrix feignRemoteHystrix = new TdsServiceSalQueryHystrix();
        feignRemoteHystrix.setHystrixEx(throwable);
        return feignRemoteHystrix;
    }
}
