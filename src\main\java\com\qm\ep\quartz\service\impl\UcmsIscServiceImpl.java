package com.qm.ep.quartz.service.impl;

import com.qm.ep.quartz.remote.UcmsIscFeignRemote;
import com.qm.ep.quartz.service.UcmsIscService;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UcmsIscServiceImpl implements UcmsIscService {
    @Autowired
    private UcmsIscFeignRemote ucmsIscFeignRemote;

    static final String tenantIdBT = "17";

    /**
     * 第一车网品牌数据接口（商用车）
     */
    @Override
    public JsonResultVo getIautosCarRootSY() {
        return ucmsIscFeignRemote.getIautosCarRootSY(tenantIdBT);
    }

    /**
     * 第一车网厂商车系数据接口（商用车）
     */
    @Override
    public JsonResultVo getIautosCarBrandSeriesSY() {
        return ucmsIscFeignRemote.getIautosCarBrandSeriesSY(tenantIdBT);
    }

    /**
     * 第一车网车型数据接口（商用车）
     */
    @Override
    public JsonResultVo getIautosCarTypeSY() {
        return ucmsIscFeignRemote.getIautosCarTypeSY(tenantIdBT);
    }

    /**
     * 第一车网品牌数据接口（乘用车）
     */
    @Override
    public JsonResultVo getIautosCarRootCY() {
        return ucmsIscFeignRemote.getIautosCarRootCY(tenantIdBT);
    }

    /**
     * 第一车网厂商车系数据接口（乘用车）
     */
    @Override
    public JsonResultVo getIautosCarBrandSeriesCY() {
        return ucmsIscFeignRemote.getIautosCarBrandSeriesCY(tenantIdBT);
    }

    /**
     * 第一车网车型数据接口（乘用车）
     */
    @Override
    public JsonResultVo getIautosCarTypeCY() {
        return ucmsIscFeignRemote.getIautosCarTypeCY(tenantIdBT);
    }

    /**
     * 获取旧车厂商
     */
    @Override
    public JsonResultVo getOCarBrand() {
        return ucmsIscFeignRemote.getOCarBrand(tenantIdBT);
    }

    /**
     * 获取旧车车系
     */
    @Override
    public JsonResultVo getOCarSeries() {
        return ucmsIscFeignRemote.getOCarSeries(tenantIdBT);
    }

    /**
     * 获取旧车车型
     */
    @Override
    public JsonResultVo getOCarType() {
        return ucmsIscFeignRemote.getOCarType(tenantIdBT);
    }
}
